#!/usr/bin/env python3
"""
测试i18n日志系统
"""

import logging
import io
import sys
from unittest.mock import patch

# 直接复制核心代码进行测试
import os
from typing import Dict, Any, Optional

# 消息配置（简化版）
MESSAGES = {
    "zh_CN": {
        "logs": {
            "workflow": {
                "supervision_start": "开始工作流监督",
                "supervision_complete": "工作流监督完成，路由到 {destination}，原因: {reason}",
                "supervision_failed": "工作流监督失败: {error}"
            },
            "steps": {
                "step_start": "执行步骤 {step_index}: {step_title}",
                "step_complete": "完成步骤: {step_title}",
                "step_failed": "步骤 {step_title} 失败: {error}"
            },
            "system": {
                "token_generated": "为用户 {user_id} 生成动态JWT token",
                "mcp_tools_loaded": "创建React agent，加载了 {tool_count} 个MCP工具"
            }
        }
    },
    "en_US": {
        "logs": {
            "workflow": {
                "supervision_start": "Starting workflow supervision",
                "supervision_complete": "Workflow supervision completed, routing to {destination}, reason: {reason}",
                "supervision_failed": "Workflow supervision failed: {error}"
            },
            "steps": {
                "step_start": "Executing step {step_index}: {step_title}",
                "step_complete": "Completed step: {step_title}",
                "step_failed": "Step {step_title} failed: {error}"
            },
            "system": {
                "token_generated": "Generated dynamic JWT token for user: {user_id}",
                "mcp_tools_loaded": "Created React agent with {tool_count} MCP tools"
            }
        }
    }
}

DEFAULT_LANGUAGE = "zh_CN"

class MessageManager:
    def __init__(self, language: str = None):
        self.language = language or DEFAULT_LANGUAGE
    
    def get_message(self, key: str, **kwargs) -> str:
        try:
            keys = key.split(".")
            messages = MESSAGES.get(self.language, MESSAGES[DEFAULT_LANGUAGE])
            
            current = messages
            for k in keys:
                current = current[k]
            
            if isinstance(current, str):
                return current.format(**kwargs)
            else:
                return str(current)
        except (KeyError, TypeError, ValueError):
            return f"[{key}]"

_message_manager = MessageManager()

def set_language(language: str):
    global _message_manager
    _message_manager = MessageManager(language)

class I18nLogger:
    """国际化日志器"""
    
    def __init__(self, component_name: str, logger_name: Optional[str] = None, language: str = None):
        self.component_name = component_name
        self.logger_name = logger_name or f"brand_event.{component_name.lower()}"
        self.logger = logging.getLogger(self.logger_name)
        self.message_manager = MessageManager(language) if language else _message_manager
    
    def _format_message(self, message: str, session_id: Optional[str] = None, 
                       context: Optional[Dict[str, Any]] = None) -> str:
        parts = []
        
        if session_id:
            parts.append(f"[Session:{session_id}]")
        
        parts.append(f"[{self.component_name}]")
        
        if context:
            for key, value in context.items():
                parts.append(f"[{key}:{value}]")
        
        parts.append(message)
        return " ".join(parts)
    
    def _get_log_message(self, key: str, **kwargs) -> str:
        return self.message_manager.get_message(key, **kwargs)
    
    def info(self, key: str, session_id: Optional[str] = None, 
             context: Optional[Dict[str, Any]] = None, **kwargs):
        message = self._get_log_message(key, **kwargs)
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.info(formatted_msg)
    
    def error(self, key: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None, exc_info: bool = False, **kwargs):
        message = self._get_log_message(key, **kwargs)
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.error(formatted_msg, exc_info=exc_info)

def get_i18n_logger(component_name: str, logger_name: Optional[str] = None, language: str = None) -> I18nLogger:
    return I18nLogger(component_name, logger_name, language)


def test_basic_i18n_logging():
    """测试基本i18n日志功能"""
    print("🧪 测试基本i18n日志功能...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_i18n_logger("TestComponent")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试基本日志
    logger.info("logs.workflow.supervision_start", session_id="test_session")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"日志输出: {log_output.strip()}")
    
    # 验证日志格式
    assert "[Session:test_session]" in log_output, "会话ID应该在日志中"
    assert "[TestComponent]" in log_output, "组件名应该在日志中"
    assert "开始工作流监督" in log_output, "中文消息应该在日志中"
    
    print("✅ 基本i18n日志功能测试通过")
    return True


def test_parameterized_logging():
    """测试参数化日志"""
    print("\n🧪 测试参数化日志...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_i18n_logger("ExecutionNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试参数化日志
    logger.info("logs.workflow.supervision_complete", 
               session_id="test_session",
               destination="intent_clarification",
               reason="需要澄清用户意图")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"参数化日志输出: {log_output.strip()}")
    
    # 验证参数化消息
    assert "intent_clarification" in log_output, "destination参数应该在日志中"
    assert "需要澄清用户意图" in log_output, "reason参数应该在日志中"
    
    print("✅ 参数化日志测试通过")
    return True


def test_language_switching_in_logging():
    """测试日志语言切换"""
    print("\n🧪 测试日志语言切换...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 测试中文日志
    logger_zh = get_i18n_logger("SupervisorNode")
    logger_zh.logger.addHandler(handler)
    logger_zh.logger.setLevel(logging.INFO)
    
    logger_zh.info("logs.workflow.supervision_start", session_id="test_session")
    zh_output = log_stream.getvalue()
    print(f"中文日志: {zh_output.strip()}")
    
    # 清空日志流
    log_stream.truncate(0)
    log_stream.seek(0)
    
    # 切换到英文
    set_language("en_US")
    logger_en = get_i18n_logger("SupervisorNode")
    logger_en.logger.addHandler(handler)
    logger_en.logger.setLevel(logging.INFO)
    
    logger_en.info("logs.workflow.supervision_start", session_id="test_session")
    en_output = log_stream.getvalue()
    print(f"英文日志: {en_output.strip()}")
    
    # 验证语言切换
    assert "开始工作流监督" in zh_output, "中文日志应该包含中文消息"
    assert "Starting workflow supervision" in en_output, "英文日志应该包含英文消息"
    assert zh_output != en_output, "不同语言的日志应该不同"
    
    # 切换回中文
    set_language("zh_CN")
    
    print("✅ 日志语言切换测试通过")
    return True


def test_context_and_session_formatting():
    """测试上下文和会话格式化"""
    print("\n🧪 测试上下文和会话格式化...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_i18n_logger("ReportNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试带上下文的日志
    context = {"task_id": "task_123", "user_id": "user_456"}
    logger.info("logs.system.token_generated", 
               session_id="test_session",
               context=context,
               user_id="user_456")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"上下文日志输出: {log_output.strip()}")
    
    # 验证格式
    assert "[Session:test_session]" in log_output, "会话ID格式错误"
    assert "[ReportNode]" in log_output, "组件名格式错误"
    assert "[task_id:task_123]" in log_output, "上下文task_id格式错误"
    assert "[user_id:user_456]" in log_output, "上下文user_id格式错误"
    assert "为用户 user_456 生成动态JWT token" in log_output, "消息内容错误"
    
    print("✅ 上下文和会话格式化测试通过")
    return True


def test_error_logging():
    """测试错误日志"""
    print("\n🧪 测试错误日志...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.ERROR)
    
    # 创建日志器
    logger = get_i18n_logger("ExecutionNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.ERROR)
    
    # 测试错误日志
    error_message = "网络连接超时"
    logger.error("logs.workflow.supervision_failed", 
                session_id="test_session",
                error=error_message)
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"错误日志输出: {log_output.strip()}")
    
    # 验证错误日志
    assert "[Session:test_session]" in log_output, "错误日志应该包含会话ID"
    assert "[ExecutionNode]" in log_output, "错误日志应该包含组件名"
    assert "工作流监督失败" in log_output, "错误日志应该包含错误消息"
    assert error_message in log_output, "错误日志应该包含具体错误信息"
    
    print("✅ 错误日志测试通过")
    return True


def test_real_workflow_scenario():
    """测试真实workflow场景"""
    print("\n🧪 测试真实workflow场景...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 模拟supervisor节点
    supervisor_logger = get_i18n_logger("SupervisorNode")
    supervisor_logger.logger.addHandler(handler)
    supervisor_logger.logger.setLevel(logging.INFO)
    
    session_id = "workflow_test_session"
    
    # 开始监督
    supervisor_logger.info("logs.workflow.supervision_start", session_id=session_id)
    
    # 完成监督
    supervisor_logger.info("logs.workflow.supervision_complete", 
                          session_id=session_id,
                          destination="execution",
                          reason="用户意图明确")
    
    # 模拟execution节点
    execution_logger = get_i18n_logger("ExecutionNode")
    execution_logger.logger.addHandler(handler)
    execution_logger.logger.setLevel(logging.INFO)
    
    # 执行步骤
    for step_index in range(2):
        step_title = f"步骤{step_index + 1}"
        execution_logger.info("logs.steps.step_start", 
                             session_id=session_id,
                             step_index=step_index,
                             step_title=step_title)
        
        execution_logger.info("logs.steps.step_complete", 
                             session_id=session_id,
                             step_title=step_title)
    
    # 获取所有日志输出
    log_output = log_stream.getvalue()
    log_lines = log_output.strip().split('\n')
    
    print(f"工作流日志输出 ({len(log_lines)} 行):")
    for i, line in enumerate(log_lines, 1):
        print(f"  {i}. {line}")
    
    # 验证日志数量和内容
    assert len(log_lines) == 6, f"应该有6行日志，实际: {len(log_lines)}"
    assert "开始工作流监督" in log_output, "应该包含监督开始日志"
    assert "工作流监督完成" in log_output, "应该包含监督完成日志"
    assert "执行步骤" in log_output, "应该包含步骤执行日志"
    assert "完成步骤" in log_output, "应该包含步骤完成日志"
    
    print("✅ 真实workflow场景测试通过")
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试i18n日志系统")
    print("=" * 50)
    
    # 设置基本日志配置
    logging.basicConfig(level=logging.DEBUG, format='%(message)s')
    
    # 运行所有测试
    tests = [
        test_basic_i18n_logging,
        test_parameterized_logging,
        test_language_switching_in_logging,
        test_context_and_session_formatting,
        test_error_logging,
        test_real_workflow_scenario,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！i18n日志系统工作正常")
        print("\n✅ 功能验证:")
        print("  - 基本i18n日志功能 ✓")
        print("  - 参数化日志消息 ✓")
        print("  - 日志语言切换 ✓")
        print("  - 上下文和会话格式化 ✓")
        print("  - 错误日志处理 ✓")
        print("  - 真实workflow场景 ✓")
        print("\n🎯 设计特点:")
        print("  - 参考SpecificLogger的格式化思路")
        print("  - 支持会话ID和上下文信息")
        print("  - 统一的日志消息管理")
        print("  - 多语言日志支持")
        print("  - 与现有日志系统兼容")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
