"""
展示如何更新workflow.py中的消息和日志的示例
"""

# 这是一个示例文件，展示如何将现有的workflow.py更新为使用新的i18n消息和标准化日志

# ==================== 更新前后对比 ====================

# 【更新前】supervisor_node 示例
def old_supervisor_node(state, writer, config):
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing workflow supervision, session_id:{session_id}")
    
    try:
        # 硬编码的消息
        writer({"live_status_message": "正在分析当前状态和用户消息..."})
        writer({"live_status_message": "正在分析用户消息类型和意图..."})
        
        # 硬编码的日志
        logger.info(f"User message analysis: type={message_type}, session_id:{session_id}")
        logger.info(f"LLM routing decision: {next}, reason: {reason}, session_id:{session_id}")
        
        # 硬编码的用户消息
        if greeting:
            writer({"agent_message": response.response_message})
            writer({"human_feedback_message": "请输入具体任务需求，我将为您提供帮助。"})
            
        logger.info(f"Completed workflow supervision, result: route to {result.goto}, reason: {route_reason}, session_id:{session_id}")
        
    except Exception as e:
        logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")
        writer({"live_status_message": "路由分析遇到问题，使用默认策略"})


# 【更新后】supervisor_node 示例
def new_supervisor_node(state, writer, config):
    from .messages import agent_msg, status_msg, feedback_msg
    from .logger import get_workflow_logger, LogMessages
    
    session_id = state.get('session_id', 'unknown')
    
    # 使用新的标准化日志
    logger = get_workflow_logger("SupervisorNode")
    logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)
    
    try:
        # 使用新的i18n消息系统
        writer(status_msg("supervisor.analyzing_state"))
        writer(status_msg("supervisor.analyzing_message_type"))
        
        # 使用新的标准化日志
        logger.info(LogMessages.USER_MESSAGE_ANALYSIS.format(message_type=message_type), 
                   session_id=session_id)
        logger.info(LogMessages.LLM_ROUTING_DECISION.format(next=next, reason=reason), 
                   session_id=session_id)
        
        # 使用新的i18n消息系统
        if greeting:
            writer(agent_msg("supervisor.greeting_response", message=response.response_message))
            writer(feedback_msg("supervisor.greeting_help"))
            
        # 使用新的标准化日志
        logger.info(LogMessages.WORKFLOW_SUPERVISION_COMPLETE.format(destination=result.goto, reason=route_reason), 
                   session_id=session_id)
        
    except Exception as e:
        # 使用新的标准化日志
        logger.error(LogMessages.WORKFLOW_SUPERVISION_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)
        # 使用新的i18n消息系统
        writer(status_msg("supervisor.routing_error"))


# ==================== 需要更新的所有节点 ====================

# 1. supervisor_node - ✅ 已更新
# 2. intent_clarification_node - 需要更新
# 3. planning_node - 需要更新  
# 4. execution_node - 需要更新
# 5. report_node - 需要更新

# ==================== intent_clarification_node 更新示例 ====================

def old_intent_clarification_node(state, writer, config):
    session_id = state.get('session_id', 'unknown')
    clarification_round = state.get("clarification_round", 0)
    logger.info(f"Start doing intent clarification (round {clarification_round + 1}), session_id:{session_id}")
    
    try:
        # 硬编码消息
        writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})
        writer({"live_status_message": "正在分析用户意图..."})
        writer({"live_status_message": "正在分析您的回复意图..."})
        
        # 硬编码日志
        logger.error(f"Failed to analyze user intent: {e}")
        logger.info(f"User provided supplement info, re-analyzing intent, session_id:{session_id}")
        
        # 硬编码用户消息
        writer({"agent_message": "意图已确认，将为您制定详细的执行计划"})
        writer({"agent_message": "我理解您提供的补充信息，让我重新分析您的需求。"})
        writer({"human_feedback_message": "请提供更多详细信息，以便我为您制定最佳的分析方案。"})
        
    except Exception as e:
        logger.error(f"Failed intent clarification, error: {str(e)}, session_id:{session_id}")


def new_intent_clarification_node(state, writer, config):
    from .messages import agent_msg, status_msg, feedback_msg
    from .logger import get_workflow_logger, LogMessages
    
    session_id = state.get('session_id', 'unknown')
    clarification_round = state.get("clarification_round", 0)
    
    # 使用新的标准化日志
    logger = get_workflow_logger("IntentClarificationNode")
    logger.info(LogMessages.WORKFLOW_INTENT_START.format(round=clarification_round + 1), session_id=session_id)
    
    try:
        # 使用新的i18n消息系统
        writer(agent_msg("intent.analyzing_requirements"))
        writer(status_msg("intent.analyzing"))
        writer(status_msg("intent.analyzing_reply"))
        
        # 使用新的标准化日志
        logger.error(LogMessages.ERROR_INTENT_ANALYSIS_FAILED.format(error=str(e)), session_id=session_id)
        logger.info(LogMessages.USER_FEEDBACK_RECEIVED, session_id=session_id)
        
        # 使用新的i18n消息系统
        writer(agent_msg("intent.confirmed"))
        writer(agent_msg("intent.supplement_understood"))
        writer(feedback_msg("intent.need_more_info"))
        
    except Exception as e:
        # 使用新的标准化日志
        logger.error(LogMessages.WORKFLOW_INTENT_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)


# ==================== planning_node 更新示例 ====================

def old_planning_node(state, writer, config):
    session_id = state.get('session_id', 'unknown')
    planning_round = state.get("planning_round", 0)
    logger.info(f"Start doing planning (round {planning_round + 1}), session_id:{session_id}")
    
    try:
        # 硬编码消息
        writer({"agent_message": "正在为您制定详细的执行计划。"})
        writer({"live_status_message": "正在制定执行计划..."})
        writer({"live_status_message": "正在分析您的反馈意图..."})
        writer({"live_status_message": "正在生成执行计划..."})
        
        # 硬编码日志
        logger.error(f"Failed to analyze plan feedback intent: {e}")
        logger.info(f"Completed planning, result: user approved plan, session_id:{session_id}")
        logger.error(f"Failed to create execution plan: {e}")
        
        # 硬编码用户消息
        writer({"agent_message": "计划已确认，将开始执行分析任务"})
        writer({"agent_message": "我理解您的修改建议，让我重新制定计划。"})
        writer({"human_feedback_message": "请确认执行计划，如需修改请告诉我具体调整建议。"})
        
    except Exception as e:
        logger.error(f"Failed planning, error: {str(e)}, session_id:{session_id}")


def new_planning_node(state, writer, config):
    from .messages import agent_msg, status_msg, feedback_msg
    from .logger import get_workflow_logger, LogMessages
    
    session_id = state.get('session_id', 'unknown')
    planning_round = state.get("planning_round", 0)
    
    # 使用新的标准化日志
    logger = get_workflow_logger("PlanningNode")
    logger.info(LogMessages.WORKFLOW_PLANNING_START.format(round=planning_round + 1), session_id=session_id)
    
    try:
        # 使用新的i18n消息系统
        writer(agent_msg("planning.creating_plan"))
        writer(status_msg("planning.generating"))
        writer(status_msg("planning.analyzing_feedback"))
        writer(status_msg("planning.generating_execution_plan"))
        
        # 使用新的标准化日志
        logger.error(LogMessages.ERROR_PLAN_CREATION_FAILED.format(error=str(e)), session_id=session_id)
        logger.info(LogMessages.USER_PLAN_APPROVED, session_id=session_id)
        logger.error(LogMessages.ERROR_PLAN_CREATION_FAILED.format(error=str(e)), session_id=session_id)
        
        # 使用新的i18n消息系统
        writer(agent_msg("planning.plan_confirmed"))
        writer(agent_msg("planning.modification_understood"))
        writer(feedback_msg("planning.confirm_plan"))
        
    except Exception as e:
        # 使用新的标准化日志
        logger.error(LogMessages.WORKFLOW_PLANNING_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)


# ==================== execution_node 更新示例 ====================

def old_execution_node(state, writer, config):
    session_id = state.get('session_id', 'unknown')
    
    try:
        # 硬编码日志
        logger.error(f"Failed task execution, error: no execution plan provided, session_id:{session_id}")
        logger.info(f"Start doing task execution ({total_steps} steps), session_id:{session_id}")
        logger.info(f"Generated dynamic JWT token for user: {user_id}")
        logger.info(f"Created React agent with {len(tools)} MCP tools")
        logger.info(f"Skipping step {step_index + 1}: {plan_step.title}")
        logger.info(f"Executing step {step_index}: {plan_step.title}")
        
        # 硬编码消息
        writer({"live_status_message": f"开始执行{plan_step.title}..."})
        writer({"agent_message": f"接下来我将执行第{step_index + 1}个任务：{step_title}"})
        writer({"agent_message": f"第{step_index + 1}个任务已完成：{step_result}"})
        
    except Exception as e:
        logger.error(f"Execution failed: {e}, session_id:{session_id}")


def new_execution_node(state, writer, config):
    from .messages import agent_msg, status_msg
    from .logger import get_workflow_logger, LogMessages
    
    session_id = state.get('session_id', 'unknown')
    
    # 使用新的标准化日志
    logger = get_workflow_logger("ExecutionNode")
    
    try:
        # 使用新的标准化日志
        logger.error(LogMessages.ERROR_NO_EXECUTION_PLAN, session_id=session_id)
        logger.info(LogMessages.WORKFLOW_EXECUTION_START.format(total_steps=total_steps), session_id=session_id)
        logger.info(LogMessages.JWT_TOKEN_GENERATED.format(user_id=user_id), session_id=session_id)
        logger.info(LogMessages.MCP_TOOLS_LOADED.format(tool_count=len(tools)), session_id=session_id)
        logger.info(LogMessages.STEP_SKIPPED.format(step_index=step_index + 1, step_title=plan_step.title), session_id=session_id)
        logger.info(LogMessages.STEP_START.format(step_index=step_index, step_title=plan_step.title), session_id=session_id)
        
        # 使用新的i18n消息系统
        writer(status_msg("execution.executing_step", step_name=plan_step.title))
        writer(agent_msg("execution.step_start", step_index=step_index + 1, step_title=step_title))
        writer(agent_msg("execution.step_completed", step_index=step_index + 1, result=step_result))
        
    except Exception as e:
        # 使用新的标准化日志
        logger.error(LogMessages.WORKFLOW_EXECUTION_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)


# ==================== 总结 ====================

"""
更新workflow.py的步骤：

1. 添加导入：
   from .messages import agent_msg, status_msg, feedback_msg
   from .logger import get_workflow_logger, LogMessages

2. 在每个节点函数开始创建日志器：
   logger = get_workflow_logger("NodeName")

3. 替换硬编码的writer调用：
   # 原来：
   writer({"agent_message": "硬编码消息"})
   writer({"live_status_message": "硬编码状态"})
   writer({"human_feedback_message": "硬编码反馈"})
   
   # 改为：
   writer(agent_msg("message.key", param=value))
   writer(status_msg("message.key", param=value))
   writer(feedback_msg("message.key", param=value))

4. 替换硬编码的日志调用：
   # 原来：
   logger.info(f"硬编码日志消息, session_id:{session_id}")
   logger.error(f"硬编码错误消息: {error}, session_id:{session_id}")
   
   # 改为：
   logger.info(LogMessages.STANDARD_MESSAGE.format(param=value), session_id=session_id)
   logger.error(LogMessages.ERROR_MESSAGE.format(error=str(e)), session_id=session_id, exc_info=True)

5. 确保所有消息键在messages.py中定义
6. 确保所有日志消息在logger.py的LogMessages类中定义

优势：
- 用户界面消息支持多语言切换
- 日志消息标准化，便于监控和分析
- 消息集中管理，便于维护
- 代码更加清晰和一致
"""
