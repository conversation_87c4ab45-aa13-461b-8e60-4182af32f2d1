#!/usr/bin/env python3
"""
展示如何使用新的WorkflowConfiguration MCP配置方法
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.brand_event_agent.config import WorkflowConfiguration


def example_default_mcp_config():
    """默认MCP配置示例"""
    print("🔧 默认MCP配置示例")
    print("=" * 50)
    
    # 创建配置实例
    config = WorkflowConfiguration()
    
    # 创建MCP服务器配置
    mcp_config = config.create_mcp_server_config(
        user_id="<EMAIL>",
        session_id="test_session_123",
        task_id="task_456",
        sandbox_id="sandbox_789",
        jwt_token="jwt_token_example"
    )
    
    print(f"✅ 默认MCP配置:")
    print(f"   服务器URL: {config.mcp_server_url}")
    print(f"   超时时间: {config.mcp_timeout}秒")
    print(f"   服务器配置: {mcp_config}")
    
    return mcp_config


def example_custom_mcp_config():
    """自定义MCP配置示例"""
    print("\n⚙️ 自定义MCP配置示例")
    print("=" * 50)
    
    # 自定义MCP服务器配置
    custom_server_config = {
        "custom_brand_event": {
            "transport": "streamable_http",
            "url": "https://custom-mcp-server.example.com/mcp",
            "headers": {
                "Authorization": "Bearer custom_token",
                "Custom-Header": "custom_value"
            }
        }
    }
    
    # 创建配置实例
    config = WorkflowConfiguration(
        mcp_server_url="https://custom-mcp-server.example.com/mcp",
        mcp_timeout=600,
        mcp_server_config=custom_server_config,
        mcp_prompt="Custom MCP prompt for brand analysis",
        mcp_tools_to_include=["search_tool", "analysis_tool", "report_tool"]
    )
    
    # 创建MCP服务器配置（会使用自定义配置）
    mcp_config = config.create_mcp_server_config(
        user_id="<EMAIL>",
        session_id="custom_session"
    )
    
    print(f"✅ 自定义MCP配置:")
    print(f"   服务器URL: {config.mcp_server_url}")
    print(f"   超时时间: {config.mcp_timeout}秒")
    print(f"   自定义提示: {config.get_mcp_prompt()}")
    print(f"   包含工具: {config.get_mcp_tools_to_include()}")
    print(f"   服务器配置: {mcp_config}")
    
    return config


def example_filtered_headers():
    """过滤None值的header示例"""
    print("\n🔍 过滤None值的header示例")
    print("=" * 50)
    
    config = WorkflowConfiguration()
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "完整参数",
            "params": {
                "user_id": "<EMAIL>",
                "session_id": "session_123",
                "task_id": "task_456",
                "sandbox_id": "sandbox_789",
                "jwt_token": "jwt_token"
            }
        },
        {
            "name": "部分参数（无task_id）",
            "params": {
                "user_id": "<EMAIL>",
                "session_id": "session_123",
                "task_id": None,
                "sandbox_id": "sandbox_789",
                "jwt_token": "jwt_token"
            }
        },
        {
            "name": "最少参数",
            "params": {
                "user_id": "<EMAIL>",
                "session_id": None,
                "task_id": None,
                "sandbox_id": None,
                "jwt_token": "jwt_token"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}:")
        mcp_config = config.create_mcp_server_config(**test_case['params'])
        headers = mcp_config['brand_event']['headers']
        print(f"   Headers: {headers}")
        print(f"   Header数量: {len(headers)}")
    
    return test_cases


def example_from_runnable_config():
    """从RunnableConfig创建MCP配置示例"""
    print("\n🔄 从RunnableConfig创建MCP配置示例")
    print("=" * 50)
    
    # 模拟RunnableConfig
    runnable_config = {
        "configurable": {
            "mcp_server_url": "https://config-mcp-server.example.com/mcp",
            "mcp_timeout": 450,
            "mcp_prompt": "Config-based MCP prompt",
            "mcp_tools_to_include": ["tool1", "tool2"]
        }
    }
    
    # 从配置创建
    config = WorkflowConfiguration.from_runnable_config(runnable_config)
    
    # 创建MCP配置
    mcp_config = config.create_mcp_server_config(
        user_id="<EMAIL>",
        session_id="config_session",
        jwt_token="config_jwt_token"
    )
    
    print(f"✅ 从RunnableConfig创建的MCP配置:")
    print(f"   服务器URL: {config.mcp_server_url}")
    print(f"   超时时间: {config.mcp_timeout}秒")
    print(f"   提示: {config.get_mcp_prompt()}")
    print(f"   工具列表: {config.get_mcp_tools_to_include()}")
    print(f"   服务器配置: {mcp_config}")
    
    return config


def example_workflow_usage():
    """在workflow中的使用示例"""
    print("\n🔄 Workflow中的使用示例")
    print("=" * 50)
    
    def mock_execution_node(state, writer, config):
        """模拟execution节点中的MCP配置使用"""
        from src.brand_event_agent.config import WorkflowConfiguration
        
        # 获取配置
        configurable = WorkflowConfiguration.from_runnable_config(config)
        
        # 从状态中获取用户信息
        user_id = '<EMAIL>'
        session_id = state.get('session_id')
        task_id = state.get('task_id')
        sandbox_id = state.get('sandbox_id')
        
        # 生成JWT token（模拟）
        jwt_token = "mock_jwt_token"
        
        # 旧的方式（已废弃）:
        # server_configs = {
        #     "brand_event": {
        #         "transport": "streamable_http",
        #         "url": "http://172.21.65.95:5003/mcp/marketing",
        #         "headers": {
        #             "sessionId": session_id,
        #             "taskId": task_id,
        #             "sandbox_id": sandbox_id,
        #             "account": user_id,
        #             "sign": jwt_token,
        #         }
        #     }
        # }
        
        # 新的方式:
        server_configs = configurable.create_mcp_server_config(
            user_id=user_id,
            session_id=session_id,
            task_id=task_id,
            sandbox_id=sandbox_id,
            jwt_token=jwt_token
        )
        
        print(f"✅ Execution节点使用的MCP配置:")
        print(f"   服务器URL: {configurable.mcp_server_url}")
        print(f"   配置: {server_configs}")
        
        return server_configs
    
    # 模拟状态和配置
    test_state = {
        'session_id': 'workflow_session_123',
        'task_id': 'workflow_task_456',
        'sandbox_id': None  # 测试None值过滤
    }
    
    test_config = {
        "configurable": {
            "mcp_server_url": "http://172.21.65.95:5003/mcp/marketing",
            "mcp_timeout": 300
        }
    }
    
    # 测试使用
    mcp_config = mock_execution_node(test_state, None, test_config)
    
    return mcp_config


def main():
    """主函数"""
    print("🚀 WorkflowConfiguration MCP配置示例")
    print("=" * 60)
    
    # 运行所有示例
    examples = [
        example_default_mcp_config,
        example_custom_mcp_config,
        example_filtered_headers,
        example_from_runnable_config,
        example_workflow_usage,
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例 {example.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("✅ 新的MCP配置方法优势:")
    print("   1. 统一的MCP服务器配置管理")
    print("   2. 自动过滤None值，避免header错误")
    print("   3. 支持自定义MCP服务器配置")
    print("   4. 支持MCP提示和工具过滤配置")
    print("   5. 减少重复的配置代码")
    
    print("\n🔧 使用方法:")
    print("   # 基本使用")
    print("   config = WorkflowConfiguration()")
    print("   mcp_config = config.create_mcp_server_config(")
    print("       user_id='<EMAIL>',")
    print("       session_id='session_123',")
    print("       jwt_token='jwt_token'")
    print("   )")
    print()
    print("   # 在workflow中使用")
    print("   configurable = WorkflowConfiguration.from_runnable_config(config)")
    print("   server_configs = configurable.create_mcp_server_config(...)")


if __name__ == "__main__":
    main()
