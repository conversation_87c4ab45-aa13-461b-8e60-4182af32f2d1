#!/usr/bin/env python3
"""
运行Workflow节点测试的简化脚本
只运行关键的测试用例，验证各个分支逻辑
"""

import asyncio
import logging
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入测试用例
from test_workflow_nodes import (
    # Supervisor Node 测试
    test_supervisor_node_greeting,
    test_supervisor_node_task_request,

    # Intent Clarification Node 测试
    test_intent_clarification_first_round,
    test_intent_clarification_clear_intent,

    # Planning Node 测试
    test_planning_node_first_plan,
    test_planning_node_user_approval,

    # Execution Node 测试
    test_execution_node_no_plan,
    test_execution_node_valid_plan,
    
    # Report Node 测试
    test_report_node_dsl_failed,
    test_report_node_no_dsl_data,
)


async def run_key_tests():
    """运行关键测试用例"""
    print("🚀 开始运行Workflow节点关键测试")
    print("=" * 60)
    print("📝 说明: 这些测试使用真实的LLM调用，只mock writer")
    print("🔧 配置: 使用gpt-4o-mini模型进行测试")
    print("=" * 60)
    
    # 设置日志级别 - 减少噪音
    logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 关键测试用例 - 覆盖主要分支
    key_tests = [
        # # 1. Supervisor Node - 问候和任务请求
        # ("Supervisor - 问候消息", test_supervisor_node_greeting),
        # ("Supervisor - 任务请求", test_supervisor_node_task_request),
        #
        # # 2. Intent Clarification - 首轮澄清和明确意图
        # ("Intent - 首轮澄清", test_intent_clarification_first_round),
        # ("Intent - 明确意图", test_intent_clarification_clear_intent),
        #
        # # 3. Planning - 首次计划和用户批准
        # ("Planning - 首次制定", test_planning_node_first_plan),
        # ("Planning - 用户批准", test_planning_node_user_approval),
        
        # 4. Execution - 无计划和有效计划
        ("Execution - 无计划", test_execution_node_no_plan),
        ("Execution - 有效计划", test_execution_node_valid_plan),
        
        # 5. Report - DSL失败和无DSL数据
        ("Report - DSL失败", test_report_node_dsl_failed),
        ("Report - 无DSL数据", test_report_node_no_dsl_data),
    ]
    
    results = []
    total_tests = len(key_tests)
    
    for i, (test_name, test_func) in enumerate(key_tests, 1):
        print(f"\n{'='*60}")
        print(f"🧪 [{i}/{total_tests}] 测试: {test_name}")
        print(f"📋 函数: {test_func.__name__}")
        print("-" * 60)
        
        try:
            result = await test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                results.append(True)
            else:
                print(f"❌ {test_name} - 失败")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            print("📋 错误详情:")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    failed = total_tests - passed
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/total_tests*100:.1f}%")
    
    if passed == total_tests:
        print(f"\n🎉 所有{total_tests}个关键测试通过！")
        print("✨ Workflow节点的主要分支逻辑验证成功")
        return True
    else:
        print(f"\n⚠️ {failed}个测试失败，需要检查相关逻辑")
        return False


async def run_single_test(test_name: str):
    """运行单个测试"""
    test_mapping = {
        "supervisor_greeting": test_supervisor_node_greeting,
        "supervisor_task": test_supervisor_node_task_request,
        "intent_first": test_intent_clarification_first_round,
        "intent_clear": test_intent_clarification_clear_intent,
        "planning_first": test_planning_node_first_plan,
        "planning_approval": test_planning_node_user_approval,
        "execution_no_plan": test_execution_node_no_plan,
        "execution_valid": test_execution_node_valid_plan,
        "report_dsl_failed": test_report_node_dsl_failed,
        "report_no_dsl": test_report_node_no_dsl_data,
    }
    
    if test_name not in test_mapping:
        print(f"❌ 未找到测试: {test_name}")
        print(f"📋 可用测试: {', '.join(test_mapping.keys())}")
        return False
    
    print(f"🧪 运行单个测试: {test_name}")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    try:
        test_func = test_mapping[test_name]
        result = await test_func()
        
        if result:
            print(f"✅ 测试 {test_name} 通过")
            return True
        else:
            print(f"❌ 测试 {test_name} 失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试 {test_name} 异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def print_usage():
    """打印使用说明"""
    print("🔧 Workflow节点测试工具")
    print("=" * 40)
    print("用法:")
    print("  python run_workflow_tests.py              # 运行所有关键测试")
    print("  python run_workflow_tests.py <test_name>  # 运行单个测试")
    print()
    print("可用的单个测试:")
    print("  supervisor_greeting   - Supervisor问候消息测试")
    print("  supervisor_task       - Supervisor任务请求测试")
    print("  intent_first          - Intent首轮澄清测试")
    print("  intent_clear          - Intent明确意图测试")
    print("  planning_first        - Planning首次制定测试")
    print("  planning_approval     - Planning用户批准测试")
    print("  execution_no_plan     - Execution无计划测试")
    print("  execution_valid       - Execution有效计划测试")
    print("  report_dsl_failed     - Report DSL失败测试")
    print("  report_no_dsl         - Report无DSL数据测试")
    print()
    print("示例:")
    print("  python run_workflow_tests.py supervisor_greeting")
    print("  python run_workflow_tests.py execution_valid")


async def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 运行所有关键测试
        success = await run_key_tests()
    elif len(sys.argv) == 2:
        test_name = sys.argv[1]
        if test_name in ["-h", "--help", "help"]:
            print_usage()
            return
        # 运行单个测试
        success = await run_single_test(test_name)
    else:
        print("❌ 参数错误")
        print_usage()
        sys.exit(1)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
