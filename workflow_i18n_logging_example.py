"""
展示如何在workflow.py中使用i18n日志系统的示例
"""

from src.brand_event_agent.messages import (
    agent_msg, status_msg, feedback_msg, 
    get_i18n_logger, set_language
)

# ==================== 使用示例 ====================

def example_supervisor_node_with_i18n_logging(state, writer, config):
    """
    使用i18n日志的supervisor节点示例
    """
    # 创建i18n日志器
    logger = get_i18n_logger("SupervisorNode")
    session_id = state.get('session_id', 'unknown')
    
    # 原来的硬编码日志方式：
    # logger.info(f"Start doing workflow supervision, session_id:{session_id}")
    
    # 使用i18n的日志方式：
    logger.info("logs.workflow.supervision_start", session_id=session_id)
    
    try:
        # 发送用户界面消息
        writer(status_msg("supervisor.analyzing_state"))
        writer(agent_msg("supervisor.greeting_help"))
        
        # 模拟路由决策
        destination = "intent_clarification"
        reason = "需要澄清用户意图"
        
        # 原来的硬编码日志方式：
        # logger.info(f"Completed workflow supervision, result: route to {destination}, reason: {reason}, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.info("logs.workflow.supervision_complete", 
                   session_id=session_id, 
                   destination=destination, 
                   reason=reason)
        
        return {"goto": destination}
        
    except Exception as e:
        # 原来的硬编码日志方式：
        # logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.error("logs.workflow.supervision_failed", 
                    session_id=session_id, 
                    error=str(e), 
                    exc_info=True)
        
        writer(status_msg("supervisor.routing_error"))
        return {"goto": "intent_clarification"}


def example_execution_node_with_i18n_logging(state, writer, config):
    """
    使用i18n日志的执行节点示例
    """
    # 创建i18n日志器
    logger = get_i18n_logger("ExecutionNode")
    session_id = state.get('session_id', 'unknown')
    task_plan_dict = state.get("task_plan", {})
    
    try:
        if not task_plan_dict:
            # 原来的硬编码日志方式：
            # logger.error(f"Failed task execution, error: no execution plan provided, session_id:{session_id}")
            
            # 使用i18n的日志方式：
            logger.error("logs.errors.no_execution_plan", session_id=session_id)
            return {"goto": "__end__", "workflow_status": "FAILED"}
        
        execution_plan = task_plan_dict  # 简化处理
        total_steps = len(execution_plan.get("steps", []))
        
        # 原来的硬编码日志方式：
        # logger.info(f"Start doing task execution ({total_steps} steps), session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.info("logs.workflow.execution_start", 
                   session_id=session_id, 
                   total_steps=total_steps)
        
        # 发送用户界面消息
        writer(agent_msg("execution.starting"))
        
        # 执行步骤
        for step_index, plan_step in enumerate(execution_plan.get("steps", [])):
            step_title = plan_step.get("title", f"步骤{step_index + 1}")
            
            if plan_step.get("skip_execute", False):
                # 原来的硬编码日志方式：
                # logger.info(f"Skipping step {step_index + 1}: {step_title}, session_id:{session_id}")
                
                # 使用i18n的日志方式：
                logger.info("logs.steps.step_skipped", 
                           session_id=session_id, 
                           step_index=step_index, 
                           step_title=step_title)
                continue
            
            # 发送用户界面消息
            writer(status_msg("execution.executing_step", step_name=step_title))
            writer(agent_msg("execution.step_start", 
                           step_index=step_index + 1, 
                           step_title=step_title))
            
            try:
                # 原来的硬编码日志方式：
                # logger.info(f"Executing step {step_index}: {step_title}")
                
                # 使用i18n的日志方式：
                logger.info("logs.steps.step_start", 
                           session_id=session_id, 
                           step_index=step_index, 
                           step_title=step_title)
                
                # 模拟步骤执行
                step_result = f"步骤{step_index + 1}执行完成"
                
                # 原来的硬编码日志方式：
                # logger.info(f"Completed step: {step_title}, session_id:{session_id}")
                
                # 使用i18n的日志方式：
                logger.info("logs.steps.step_complete", 
                           session_id=session_id, 
                           step_title=step_title)
                
                # 发送用户界面消息
                writer(agent_msg("execution.step_completed", 
                               step_index=step_index + 1, 
                               result=step_result))
                
            except Exception as e:
                # 原来的硬编码日志方式：
                # logger.error(f"Step {step_index + 1} failed: {e}")
                
                # 使用i18n的日志方式：
                logger.error("logs.steps.step_failed", 
                            session_id=session_id, 
                            step_title=step_title, 
                            error=str(e))
        
        # 完成执行
        writer(agent_msg("execution.all_completed"))
        
        # 原来的硬编码日志方式：
        # logger.info(f"Execution completed, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.info("logs.workflow.execution_complete", session_id=session_id)
        
        return {"goto": "report", "workflow_status": "REPORT"}
        
    except Exception as e:
        # 原来的硬编码日志方式：
        # logger.error(f"Execution failed: {e}, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.error("logs.workflow.execution_failed", 
                    session_id=session_id, 
                    error=str(e), 
                    exc_info=True)
        
        return {"goto": "__end__", "workflow_status": "FAILED"}


def example_report_node_with_i18n_logging(state, writer, config):
    """
    使用i18n日志的报告节点示例
    """
    # 创建i18n日志器
    logger = get_i18n_logger("ReportNode")
    session_id = state.get('session_id', 'unknown')
    
    try:
        # 原来的硬编码日志方式：
        # logger.info(f"Starting final report generation, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.info("logs.workflow.report_start", session_id=session_id)
        
        # 发送用户界面消息
        writer(agent_msg("report.generating"))
        
        # 模拟系统操作日志
        logger.info("logs.system.report_service_call", session_id=session_id)
        writer(status_msg("report.calling_service"))
        
        # 模拟S3上传
        logger.info("logs.system.s3_upload_start", session_id=session_id)
        writer(status_msg("report.uploading"))
        
        # 模拟成功上传
        upload_result = {"fileKey": "report-123.html", "url": "https://example.com/report.html"}
        logger.info("logs.system.s3_upload_complete", 
                   session_id=session_id, 
                   result=str(upload_result))
        
        # 模拟DSL解析
        ai_summary_length = 150
        metrics_count = 5
        viewpoints_count = 3
        logger.info("logs.system.dsl_parsing", 
                   session_id=session_id,
                   ai_summary_length=ai_summary_length,
                   metrics_count=metrics_count,
                   viewpoints_count=viewpoints_count)
        
        writer(status_msg("report.parsing_summary"))
        
        # 完成报告生成
        writer(agent_msg("report.completed"))
        writer(feedback_msg("report.other_help"))
        
        # 原来的硬编码日志方式：
        # logger.info(f"Final report generated successfully, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.info("logs.workflow.report_complete", session_id=session_id)
        
        return {"goto": "__end__", "workflow_status": "COMPLETED"}
        
    except Exception as e:
        # 原来的硬编码日志方式：
        # logger.error(f"Failed to generate report: {e}, session_id:{session_id}")
        
        # 使用i18n的日志方式：
        logger.error("logs.workflow.report_failed", 
                    session_id=session_id, 
                    error=str(e), 
                    exc_info=True)
        
        writer(agent_msg("report.generation_failed", error=str(e)))
        return {"goto": "__end__", "workflow_status": "FAILED"}


def example_language_switching_in_logging():
    """
    日志语言切换示例
    """
    logger = get_i18n_logger("TestComponent")
    
    # 默认中文日志
    print("=== 中文日志 ===")
    logger.info("logs.workflow.supervision_start", session_id="test_session")
    logger.info("logs.steps.step_start", session_id="test_session", step_index=1, step_title="数据收集")
    
    # 切换到英文
    set_language("en_US")
    logger_en = get_i18n_logger("TestComponent")
    
    print("\n=== 英文日志 ===")
    logger_en.info("logs.workflow.supervision_start", session_id="test_session")
    logger_en.info("logs.steps.step_start", session_id="test_session", step_index=1, step_title="Data Collection")
    
    # 切换回中文
    set_language("zh_CN")


# ==================== 实际应用建议 ====================

"""
在实际的workflow.py中应用i18n日志的步骤：

1. 导入i18n日志函数：
   from .messages import get_i18n_logger, agent_msg, status_msg

2. 在每个节点函数开始创建日志器：
   logger = get_i18n_logger("SupervisorNode")  # 或其他节点名称
   session_id = state.get('session_id', 'unknown')

3. 替换硬编码日志：
   # 原来：
   logger.info(f"Start doing workflow supervision, session_id:{session_id}")
   
   # 改为：
   logger.info("logs.workflow.supervision_start", session_id=session_id)

4. 处理带参数的日志：
   # 原来：
   logger.info(f"Completed step: {step_title}, session_id:{session_id}")
   
   # 改为：
   logger.info("logs.steps.step_complete", session_id=session_id, step_title=step_title)

5. 错误日志处理：
   # 原来：
   logger.error(f"Execution failed: {e}, session_id:{session_id}")
   
   # 改为：
   logger.error("logs.workflow.execution_failed", session_id=session_id, error=str(e), exc_info=True)

优势：
- 日志消息支持多语言
- 日志格式统一，参考SpecificLogger的设计
- 消息集中管理，便于维护
- 支持参数化日志消息
- 保持与现有日志系统的兼容性
- 支持会话ID和上下文信息
"""

if __name__ == "__main__":
    # 设置基本日志配置
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 运行语言切换示例
    example_language_switching_in_logging()
