#!/usr/bin/env python3
"""
测试简化的workflow日志系统
"""

import logging
import io
import sys
import asyncio

# 直接复制核心代码进行测试
from typing import Optional, Any, Dict
from functools import wraps


class WorkflowLogger:
    """工作流日志器"""
    
    def __init__(self, component_name: str, logger_name: Optional[str] = None):
        self.component_name = component_name
        self.logger_name = logger_name or f"brand_event.{component_name.lower()}"
        self.logger = logging.getLogger(self.logger_name)
    
    def _format_message(self, message: str, session_id: Optional[str] = None, 
                       context: Optional[Dict[str, Any]] = None) -> str:
        parts = []
        
        if session_id:
            parts.append(f"[Session:{session_id}]")
        
        parts.append(f"[{self.component_name}]")
        
        if context:
            for key, value in context.items():
                parts.append(f"[{key}:{value}]")
        
        parts.append(message)
        return " ".join(parts)
    
    def info(self, message: str, session_id: Optional[str] = None, 
             context: Optional[Dict[str, Any]] = None):
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.info(formatted_msg)
    
    def error(self, message: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.error(formatted_msg, exc_info=exc_info)


def get_workflow_logger(component_name: str, logger_name: Optional[str] = None) -> WorkflowLogger:
    return WorkflowLogger(component_name, logger_name)


class LogMessages:
    """标准日志消息模板"""
    
    # Workflow operations
    WORKFLOW_SUPERVISION_START = "Start doing workflow supervision"
    WORKFLOW_SUPERVISION_COMPLETE = "Completed workflow supervision, result: route to {destination}, reason: {reason}"
    WORKFLOW_SUPERVISION_FAILED = "Failed workflow supervision, error: {error}"
    
    WORKFLOW_EXECUTION_START = "Start doing task execution ({total_steps} steps)"
    WORKFLOW_EXECUTION_COMPLETE = "Execution completed"
    WORKFLOW_EXECUTION_FAILED = "Execution failed: {error}"
    
    # Step operations
    STEP_START = "Executing step {step_index}: {step_title}"
    STEP_COMPLETE = "Completed step: {step_title}"
    STEP_FAILED = "Step {step_title} failed: {error}"
    STEP_SKIPPED = "Skipping step {step_index}: {step_title}"
    
    # System operations
    JWT_TOKEN_GENERATED = "Generated dynamic JWT token for user: {user_id}"
    MCP_TOOLS_LOADED = "Created React agent with {tool_count} MCP tools"
    S3_UPLOAD_SUCCESS = "HTML uploaded successfully: {result}"
    
    # Errors
    ERROR_NO_EXECUTION_PLAN = "Failed task execution, error: no execution plan provided"


def test_basic_workflow_logging():
    """测试基本工作流日志功能"""
    print("🧪 测试基本工作流日志功能...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_workflow_logger("SupervisorNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试基本日志
    logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id="test_session")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"日志输出: {log_output.strip()}")
    
    # 验证日志格式
    assert "[Session:test_session]" in log_output, "会话ID应该在日志中"
    assert "[SupervisorNode]" in log_output, "组件名应该在日志中"
    assert "Start doing workflow supervision" in log_output, "标准消息应该在日志中"
    
    print("✅ 基本工作流日志功能测试通过")
    return True


def test_parameterized_logging():
    """测试参数化日志"""
    print("\n🧪 测试参数化日志...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_workflow_logger("ExecutionNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试参数化日志
    logger.info(LogMessages.WORKFLOW_SUPERVISION_COMPLETE.format(
        destination="intent_clarification",
        reason="User intent needs clarification"
    ), session_id="test_session")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"参数化日志输出: {log_output.strip()}")
    
    # 验证参数化消息
    assert "intent_clarification" in log_output, "destination参数应该在日志中"
    assert "User intent needs clarification" in log_output, "reason参数应该在日志中"
    
    print("✅ 参数化日志测试通过")
    return True


def test_context_formatting():
    """测试上下文格式化"""
    print("\n🧪 测试上下文格式化...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 创建日志器
    logger = get_workflow_logger("ReportNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.INFO)
    
    # 测试带上下文的日志
    context = {"task_id": "task_123", "user_id": "user_456"}
    logger.info(LogMessages.JWT_TOKEN_GENERATED.format(user_id="user_456"), 
               session_id="test_session",
               context=context)
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"上下文日志输出: {log_output.strip()}")
    
    # 验证格式
    assert "[Session:test_session]" in log_output, "会话ID格式错误"
    assert "[ReportNode]" in log_output, "组件名格式错误"
    assert "[task_id:task_123]" in log_output, "上下文task_id格式错误"
    assert "[user_id:user_456]" in log_output, "上下文user_id格式错误"
    assert "Generated dynamic JWT token for user: user_456" in log_output, "消息内容错误"
    
    print("✅ 上下文格式化测试通过")
    return True


def test_error_logging():
    """测试错误日志"""
    print("\n🧪 测试错误日志...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.ERROR)
    
    # 创建日志器
    logger = get_workflow_logger("ExecutionNode")
    logger.logger.addHandler(handler)
    logger.logger.setLevel(logging.ERROR)
    
    # 测试错误日志
    error_message = "Network connection timeout"
    logger.error(LogMessages.WORKFLOW_SUPERVISION_FAILED.format(error=error_message), 
                session_id="test_session")
    
    # 获取日志输出
    log_output = log_stream.getvalue()
    print(f"错误日志输出: {log_output.strip()}")
    
    # 验证错误日志
    assert "[Session:test_session]" in log_output, "错误日志应该包含会话ID"
    assert "[ExecutionNode]" in log_output, "错误日志应该包含组件名"
    assert "Failed workflow supervision" in log_output, "错误日志应该包含错误消息"
    assert error_message in log_output, "错误日志应该包含具体错误信息"
    
    print("✅ 错误日志测试通过")
    return True


def test_standard_log_messages():
    """测试标准日志消息模板"""
    print("\n🧪 测试标准日志消息模板...")
    
    # 测试各种消息模板
    test_cases = [
        (LogMessages.WORKFLOW_SUPERVISION_START, "Start doing workflow supervision"),
        (LogMessages.WORKFLOW_EXECUTION_START.format(total_steps=5), "Start doing task execution (5 steps)"),
        (LogMessages.STEP_START.format(step_index=1, step_title="Data Collection"), "Executing step 1: Data Collection"),
        (LogMessages.JWT_TOKEN_GENERATED.format(user_id="test_user"), "Generated dynamic JWT token for user: test_user"),
        (LogMessages.ERROR_NO_EXECUTION_PLAN, "Failed task execution, error: no execution plan provided")
    ]
    
    for template, expected in test_cases:
        assert template == expected, f"消息模板错误: {template} != {expected}"
        print(f"✓ {template}")
    
    print("✅ 标准日志消息模板测试通过")
    return True


def test_real_workflow_scenario():
    """测试真实workflow场景"""
    print("\n🧪 测试真实workflow场景...")
    
    # 设置日志捕获
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.INFO)
    
    # 模拟supervisor节点
    supervisor_logger = get_workflow_logger("SupervisorNode")
    supervisor_logger.logger.addHandler(handler)
    supervisor_logger.logger.setLevel(logging.INFO)
    
    session_id = "workflow_test_session"
    
    # 开始监督
    supervisor_logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)
    
    # 完成监督
    supervisor_logger.info(LogMessages.WORKFLOW_SUPERVISION_COMPLETE.format(
        destination="execution", reason="User intent is clear"), session_id=session_id)
    
    # 模拟execution节点
    execution_logger = get_workflow_logger("ExecutionNode")
    execution_logger.logger.addHandler(handler)
    execution_logger.logger.setLevel(logging.INFO)
    
    # 开始执行
    execution_logger.info(LogMessages.WORKFLOW_EXECUTION_START.format(total_steps=2), 
                         session_id=session_id)
    
    # 执行步骤
    for step_index in range(2):
        step_title = f"Step{step_index + 1}"
        execution_logger.info(LogMessages.STEP_START.format(
            step_index=step_index, step_title=step_title), session_id=session_id)
        
        execution_logger.info(LogMessages.STEP_COMPLETE.format(step_title=step_title), 
                             session_id=session_id)
    
    # 完成执行
    execution_logger.info(LogMessages.WORKFLOW_EXECUTION_COMPLETE, session_id=session_id)
    
    # 获取所有日志输出
    log_output = log_stream.getvalue()
    log_lines = log_output.strip().split('\n')
    
    print(f"工作流日志输出 ({len(log_lines)} 行):")
    for i, line in enumerate(log_lines, 1):
        print(f"  {i}. {line}")
    
    # 验证日志数量和内容
    assert len(log_lines) == 8, f"应该有8行日志，实际: {len(log_lines)}"
    assert "Start doing workflow supervision" in log_output, "应该包含监督开始日志"
    assert "Completed workflow supervision" in log_output, "应该包含监督完成日志"
    assert "Start doing task execution" in log_output, "应该包含执行开始日志"
    assert "Executing step" in log_output, "应该包含步骤执行日志"
    assert "Completed step" in log_output, "应该包含步骤完成日志"
    assert "Execution completed" in log_output, "应该包含执行完成日志"
    
    print("✅ 真实workflow场景测试通过")
    return True


def test_logger_compatibility():
    """测试与现有日志系统的兼容性"""
    print("\n🧪 测试与现有日志系统的兼容性...")
    
    # 创建不同组件的日志器
    supervisor_logger = get_workflow_logger("SupervisorNode")
    execution_logger = get_workflow_logger("ExecutionNode")
    report_logger = get_workflow_logger("ReportNode")
    
    # 验证日志器名称格式
    assert supervisor_logger.logger_name == "brand_event.supervisornode", "Supervisor日志器名称格式错误"
    assert execution_logger.logger_name == "brand_event.executionnode", "Execution日志器名称格式错误"
    assert report_logger.logger_name == "brand_event.reportnode", "Report日志器名称格式错误"
    
    # 验证组件名
    assert supervisor_logger.component_name == "SupervisorNode", "Supervisor组件名错误"
    assert execution_logger.component_name == "ExecutionNode", "Execution组件名错误"
    assert report_logger.component_name == "ReportNode", "Report组件名错误"
    
    print("✅ 与现有日志系统的兼容性测试通过")
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试简化的workflow日志系统")
    print("=" * 50)
    
    # 设置基本日志配置
    logging.basicConfig(level=logging.DEBUG, format='%(message)s')
    
    # 运行所有测试
    tests = [
        test_basic_workflow_logging,
        test_parameterized_logging,
        test_context_formatting,
        test_error_logging,
        test_standard_log_messages,
        test_real_workflow_scenario,
        test_logger_compatibility,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！简化的workflow日志系统工作正常")
        print("\n✅ 功能验证:")
        print("  - 基本工作流日志功能 ✓")
        print("  - 参数化日志消息 ✓")
        print("  - 上下文格式化 ✓")
        print("  - 错误日志处理 ✓")
        print("  - 标准日志消息模板 ✓")
        print("  - 真实workflow场景 ✓")
        print("  - 与现有日志系统兼容性 ✓")
        print("\n🎯 设计特点:")
        print("  - 参考SpecificLogger的格式化思路")
        print("  - 支持会话ID和上下文信息")
        print("  - 统一的标准化日志消息")
        print("  - 不支持i18n，专注于开发运维需求")
        print("  - 与现有日志系统完全兼容")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
