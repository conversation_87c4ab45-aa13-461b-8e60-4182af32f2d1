#!/usr/bin/env python3
"""
展示如何使用新的WorkflowConfiguration方法创建ChatOpenAI实例
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.brand_event_agent.config import WorkflowConfiguration


def example_basic_usage():
    """基本使用示例"""
    print("🔧 基本使用示例")
    print("=" * 50)
    
    # 创建配置实例
    config = WorkflowConfiguration()
    
    # 使用通用方法创建LLM实例
    llm = config.create_llm('azure-gpt-4o-mini', temperature=0.0)
    
    print(f"✅ 创建的LLM实例:")
    print(f"   模型: {llm.model_name}")
    print(f"   Base URL: {llm.openai_api_base}")
    print(f"   Temperature: {llm.temperature}")
    print(f"   API Key: {llm.openai_api_key}")
    
    return llm


def example_specific_models():
    """使用特定模型的便捷方法"""
    print("\n🎯 特定模型便捷方法示例")
    print("=" * 50)
    
    # 创建配置实例
    config = WorkflowConfiguration()
    
    # 使用便捷方法创建不同角色的LLM实例
    models = {
        "Supervisor": config.get_supervisor_llm(),
        "Analyst": config.get_analyst_llm(),
        "Planner": config.get_planner_llm(),
        "Executor": config.get_executor_llm(),
        "Summarizer": config.get_summarizer_llm(),
        "Reporter": config.get_reporter_llm(),
    }
    
    for role, llm in models.items():
        print(f"✅ {role} LLM:")
        print(f"   模型: {llm.model_name}")
        print(f"   Temperature: {llm.temperature}")
    
    return models


def example_custom_config():
    """自定义配置示例"""
    print("\n⚙️ 自定义配置示例")
    print("=" * 50)
    
    # 从环境变量创建配置
    config = WorkflowConfiguration.from_runnable_config({
        "configurable": {
            "supervisor_model": "azure-gpt-4o",
            "analyst_model": "azure-gpt-4o-mini",
            "openai_api_key": "custom_key",
            "openai_base_url": "https://custom.api.endpoint.com",
        }
    })
    
    # 创建不同温度的LLM实例
    creative_llm = config.get_supervisor_llm(temperature=0.7)
    precise_llm = config.get_analyst_llm(temperature=0.0)
    
    print(f"✅ 创意型LLM (Supervisor):")
    print(f"   模型: {creative_llm.model_name}")
    print(f"   Temperature: {creative_llm.temperature}")
    print(f"   Base URL: {creative_llm.openai_api_base}")
    
    print(f"✅ 精确型LLM (Analyst):")
    print(f"   模型: {precise_llm.model_name}")
    print(f"   Temperature: {precise_llm.temperature}")
    
    return config


def example_workflow_usage():
    """在workflow中的使用示例"""
    print("\n🔄 Workflow中的使用示例")
    print("=" * 50)
    
    # 模拟workflow节点中的使用
    def mock_supervisor_node(state, writer, config):
        """模拟supervisor节点"""
        configurable = WorkflowConfiguration.from_runnable_config(config)
        
        # 旧的方式（已废弃）:
        # llm = ChatOpenAI(
        #     model=configurable.supervisor_model,
        #     temperature=0
        # )
        
        # 新的方式:
        llm = configurable.get_supervisor_llm(temperature=0)
        
        print(f"✅ Supervisor节点使用的LLM:")
        print(f"   模型: {llm.model_name}")
        print(f"   Base URL: {llm.openai_api_base}")
        print(f"   API Key: {str(llm.openai_api_key)[:10]}...")
        
        return llm
    
    def mock_execution_node(state, writer, config):
        """模拟execution节点"""
        configurable = WorkflowConfiguration.from_runnable_config(config)
        
        # 创建React agent时使用
        llm = configurable.get_executor_llm(temperature=0)
        
        # 或者fallback时使用
        fallback_llm = configurable.get_executor_llm(temperature=0)
        
        print(f"✅ Execution节点使用的LLM:")
        print(f"   模型: {llm.model_name}")
        print(f"   Temperature: {llm.temperature}")
        
        return llm
    
    # 模拟配置
    test_config = {
        "configurable": {
            "supervisor_model": "azure-gpt-4o-mini",
            "executor_model": "azure-gpt-4o-mini",
            "openai_api_key": "test",
            "openai_base_url": "https://llm-model-proxy.dev.fc.chj.cloud/agentops"
        }
    }
    
    # 测试使用
    supervisor_llm = mock_supervisor_node({}, None, test_config)
    executor_llm = mock_execution_node({}, None, test_config)
    
    return supervisor_llm, executor_llm


def example_error_handling():
    """错误处理示例"""
    print("\n⚠️ 错误处理示例")
    print("=" * 50)
    
    try:
        # 创建配置
        config = WorkflowConfiguration()
        
        # 尝试创建LLM实例
        llm = config.create_llm('azure-gpt-4o-mini')
        
        print(f"✅ LLM创建成功:")
        print(f"   模型: {llm.model_name}")
        print(f"   配置正确")
        
    except Exception as e:
        print(f"❌ LLM创建失败: {e}")
        print("   请检查配置参数")
    
    return True


def main():
    """主函数"""
    print("🚀 WorkflowConfiguration ChatOpenAI 创建示例")
    print("=" * 60)
    
    # 运行所有示例
    examples = [
        example_basic_usage,
        example_specific_models,
        example_custom_config,
        example_workflow_usage,
        example_error_handling,
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例 {example.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("✅ 新的配置方法优势:")
    print("   1. 统一的base_url和api_key配置")
    print("   2. 便捷的角色特定LLM创建方法")
    print("   3. 支持自定义temperature参数")
    print("   4. 减少重复的配置代码")
    print("   5. 更好的配置管理和维护")
    
    print("\n🔧 使用方法:")
    print("   # 基本使用")
    print("   config = WorkflowConfiguration()")
    print("   llm = config.create_llm('azure-gpt-4o-mini')")
    print()
    print("   # 便捷方法")
    print("   supervisor_llm = config.get_supervisor_llm()")
    print("   executor_llm = config.get_executor_llm(temperature=0.1)")
    print()
    print("   # 在workflow中使用")
    print("   configurable = WorkflowConfiguration.from_runnable_config(config)")
    print("   llm = configurable.get_supervisor_llm(temperature=0)")


if __name__ == "__main__":
    main()
