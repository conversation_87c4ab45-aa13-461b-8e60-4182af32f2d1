"""
事件服务处理器模块。
提供发送事件到事件服务的简化接口。
适配SpecificState系统。
使用HTTP请求直接发送事件，不依赖外部SDK。
"""

import logging
import os
import uuid
from typing import Dict, List, Any, Optional, Union

# 导入我们的状态类型
from src.models.state import SpecificState, ExecutionPlan
from agentops_event_sdk_python import EventManager as AgentOpsEventManager, AgentStatus
from agentops_event_sdk_python import EventContext, SyncWebhookEventSubscriber, ConsoleEventSubscriber
from agentops_event_sdk_python.events import (
    LiveStatusEvent, ChatEvent, ChatMessageType, ChatSender, ChatAttachment,
    AgentStatusEvent, AgentStatus as AgentOpsAgentStatus,
    PlanUpdateEvent, PlanStep, PlanStepStatus,
    NewPlanStepEvent,
    ToolUsedEvent, ToolUsedMessage, ToolUsedStatus, ToolType
)


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 工具函数 - 适配 langgraph
def log_error(logger, message):
    """记录错误信息"""
    logger.error(message)


def log_event(logger, message):
    """记录事件信息"""
    logger.info(message)


def get_context(state):
    """
    从 SpecificState 状态中提取会话数据

    Args:
        state: SpecificState对象或字典

    Returns:
        Dict: 包含会话信息的字典
    """
    # 基本会话信息
    session_data = {}

    # 判断state是字典还是对象
    if isinstance(state, dict):
        # 如果是字典，直接使用get方法
        session_data["session_id"] = state.get("session_id", "None")
        session_data["task_id"] = state.get("task_id", "None")
        session_data["sandbox_id"] = state.get("sandbox_id", "None")
        session_data["extensions"] = state.get("extensions", {})
    else:
        # 如果是对象，使用getattr方法
        session_data["session_id"] = getattr(state, "session_id", "None")
        session_data["task_id"] = getattr(state, "task_id", "None")
        session_data["sandbox_id"] = getattr(state, "sandbox_id", "None")
        session_data["extensions"] = getattr(state, "extensions", {})

    return session_data


class ManusEventHandler:
    """
    事件服务处理器类。
    提供发送事件到事件服务的简化接口，使用HTTP直接发送事件。
    """

    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ManusEventHandler, cls).__new__(cls)
        return cls._instance

    def __init__(self,
                 webhook_url: Optional[str] = None,
                 console_output: bool = True):
        """
        初始化事件服务处理器

        Args:
            webhook_url: Webhook URL，如果为None则使用环境变量
            console_output: 是否输出到控制台
        """
        if not self._initialized:
            self.webhook_url = webhook_url
            self.console_output = console_output
            self.event_manager = None  # 初始化为None，稍后在initialize中设置
            self._initialized = True

            # 初始化事件管理器
            self.initialize(webhook_url, console_output)

    def initialize(self, webhook_url: Optional[str] = None,
                   console_output: bool = True,
                   state = None) -> None:
        """
        初始化事件管理器。

        Args:
            webhook_url: Webhook URL，如果为None则使用环境变量
            console_output: 是否输出到控制台
            state: SpecificState对象，用于提取事件回调地址
        """
        # 如果未提供webhook_url，尝试从状态或环境变量获取
        if webhook_url is None:
            # 先从状态中获取
            if state:
                if isinstance(state, dict):
                    webhook_url = state.get("event_webhook")
                else:
                    webhook_url = getattr(state, "event_webhook", None)

            if not webhook_url:
                # 如果状态中没有，则从环境变量获取
                webhook_url = os.getenv("EVENT_WEBHOOK_URL") or os.getenv(
                    "XUANJI_BACKEND_EVENT_ADDRESS",
                    "http://xuanji-backend-service-dev.inner.chj.cloud/api/v1/agent-events"
                )

        # 存储配置
        self.webhook_url = webhook_url
        self.console_output = console_output

        # 初始化事件管理器
        try:
            # 创建事件订阅者列表
            subscribers = []

            # 添加Webhook订阅者（如果有URL）
            if webhook_url:
                webhook_subscriber = SyncWebhookEventSubscriber(webhook_url)
                subscribers.append(webhook_subscriber)

            # 添加控制台订阅者（如果启用）
            if console_output:
                console_subscriber = ConsoleEventSubscriber()
                subscribers.append(console_subscriber)

            # 创建事件管理器
            if subscribers:
                self.event_manager = AgentOpsEventManager(subscribers=subscribers)
                logger.info(f"事件管理器已初始化，Webhook URL: {webhook_url}, 控制台输出: {console_output}")
            else:
                logger.warning("没有配置任何事件订阅者，事件管理器将为None")
                self.event_manager = None

        except Exception as e:
            logger.error(f"初始化事件管理器失败: {e}")
            self.event_manager = None

    # ===== 核心事件发送方法 =====


    def send_live_status(self, text: str, state: Optional[SpecificState] = None) -> None:
        """
        发送实时状态事件。

        Args:
            text: 状态文本
            state: langgraph State对象，用于提取会话信息
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None

        event = LiveStatusEvent(text=text)
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送LiveStatusEvent: {text[:50]}... | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")

    def send_agent_status(self, status: Union[str, AgentStatus],
                          brief: str,
                          description: Optional[str] = None,
                          state: Optional[SpecificState] = None) -> None:
        """
        发送代理状态事件。

        Args:
            status: 代理状态，可以是字符串或AgentStatus枚举
            brief: 简短描述
            description: 详细描述
            state: langgraph State对象，用于提取会话信息
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None


        event = AgentStatusEvent(
            agent_status=status,
            brief=brief,
            description=description,
            plan_step_id=context.get("plan_step_id") if context else None
        )
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送AgentStatusEvent: {status} - {brief} | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")

    def send_user_message(self, state: SpecificState, content: str) -> None:
        """
        发送用户消息事件。

        Args:
            state: SpecificState对象，用于提取会话信息
            content: 消息内容
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None

        event = ChatEvent(
            message_type=ChatMessageType.TEXT,
            sender=ChatSender.USER,
            content=content
        )
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送User ChatEvent: {content[:50]}... | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")

    def send_agent_message(self, state: SpecificState, content: str,
                           attachments: Optional[List[ChatAttachment]] = None) -> None:
        """
        发送助手消息事件。

        Args:
            state: SpecificState对象，用于提取会话信息
            content: 消息内容
            attachments: 可选的附件列表
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None

        event = ChatEvent(
            message_type=ChatMessageType.TEXT,
            sender=ChatSender.ASSISTANT,
            content=content,
            attachments=attachments
        )
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送Assistant ChatEvent: {content[:50]}... | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")

    def send_plan_update(self, state: SpecificState, plan: ExecutionPlan) -> None:
        """
        发送计划更新事件。

        Args:
            state: SpecificState对象，用于提取会话信息
            plan: ExecutionPlan对象，包含计划ID和步骤列表
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None

        # 将ExecutionPlan对象中的步骤转换为PlanStep对象
        steps = [
            PlanStep(
                title=step.title,
                id=str(uuid.uuid4()),
                status=PlanStepStatus.TODO  # 默认为TODO状态
            ) for step in plan.steps
        ]

        # 生成计划ID
        plan_id = str(uuid.uuid4())

        event = PlanUpdateEvent(
            plan_id=plan_id,
            steps=steps
        )
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送PlanUpdateEvent: {plan_id}, {len(steps)}个步骤 | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")

    def send_new_plan_step(self, state: SpecificState,
                           plan_id: str,
                           step: PlanStep,
                           step_index: int,
                           total_steps: int,
                           status: Union[str, PlanStepStatus] = PlanStepStatus.DOING,
                           ) -> None:
        """
        发送新计划步骤事件。

        Args:
            state: SpecificState对象，用于提取会话信息
            plan_id: 计划ID
            step: 步骤
            step_index: 步骤索引
            total_steps: 总步骤数
            status: 步骤状态，默认为DOING
        """
        if self.event_manager is None:
            self.initialize(state=state)

        # 从 State 对象中提取会话信息
        context = get_context(state) if state else None

        # 将字符串状态转换为枚举
        if isinstance(status, str):
            status_map = {
                "TODO": PlanStepStatus.TODO,
                "DOING": PlanStepStatus.DOING,
                "DONE": PlanStepStatus.DONE,
            }
            step_status = status_map.get(status.upper(), PlanStepStatus.DOING)
        else:
            step_status = status

        event = NewPlanStepEvent(
            plan_id=plan_id,
            step_id=str(uuid.uuid4()),
            title=step.title,
            step=step
        )
        self._publish_event(event, context)
        session_id = context.get("session_id")
        sandbox_id = context.get("sandbox_id")
        task_id = context.get("task_id")
        logger.info(
            f"发送NewPlanStepEvent: 步骤{step_index + 1}/{total_steps} - {step.title} - {step_status} | session_id: {session_id}, sandbox_id: {sandbox_id}, task_id: {task_id}")


    # ===== 辅助方法 =====

    def _publish_event(self, event, context: Optional[Dict[str, Any]] = None) -> None:
        """
        发布事件的通用方法（用于AgentOps事件对象）。

        Args:
            event: 要发布的事件（AgentOps事件对象）
            context: 上下文字典
        """
        if self.event_manager is None:
            logger.warning("事件管理器未初始化，无法发布事件")
            return

        try:
            if context:
                event_context = EventContext(**context)
                self.event_manager.publish_sync(event, context=event_context)
            else:
                self.event_manager.publish_sync(event)
        except Exception as e:
            logger.error(f"发布事件失败: {e}")

    def _send_event(self, event_data: Dict[str, Any]) -> None:
        """
        发送事件的通用方法（用于字典格式的事件数据）。

        Args:
            event_data: 事件数据字典
        """
        if self.event_manager is None:
            logger.warning("事件管理器未初始化，无法发送事件")
            return

        try:
            # 将字典数据转换为适当的事件对象
            event_type = event_data.get("event_type")

            if event_type == "agent_status":
                # 创建AgentStatusEvent
                agent_status_str = event_data.get("agent_status", "WAITING")
                # 映射到AgentOps的状态枚举
                status_mapping = {
                    "WAITING": AgentOpsAgentStatus.WAITING,
                    "RUNNING": AgentOpsAgentStatus.RUNNING,
                    "STOPPED": AgentOpsAgentStatus.STOPPED
                }
                agent_status = status_mapping.get(agent_status_str, AgentOpsAgentStatus.WAITING)

                event = AgentStatusEvent(
                    status=agent_status,
                    brief=event_data.get("brief", ""),
                    description=event_data.get("description")
                )
            elif event_type == "live_status":
                # 创建LiveStatusEvent
                event = LiveStatusEvent(
                    text=event_data.get("content", "")
                )
            else:
                logger.warning(f"未知的事件类型: {event_type}")
                return

            # 提取上下文信息
            context_data = {
                "session_id": event_data.get("session_id"),
                "task_id": event_data.get("task_id"),
                "sandbox_id": event_data.get("sandbox_id"),
                "extensions": event_data.get("extensions", {})
            }

            # 发布事件
            self._publish_event(event, context_data)

        except Exception as e:
            logger.error(f"发送事件失败: {e}")


# 创建全局事件服务处理器实例
event_handler = ManusEventHandler()
