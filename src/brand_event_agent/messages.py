"""
国际化消息管理系统
支持多语言消息和参数化消息
"""

import os
from typing import Dict, Any, Optional

# 默认语言
DEFAULT_LANGUAGE = "zh_CN"

# 消息配置
MESSAGES = {
    "zh_CN": {
        # 监督节点消息
        "supervisor": {
            "analyzing_state": "正在分析当前状态和用户消息...",
            "analyzing_message_type": "正在分析用户消息类型和意图...",
            "routing_error": "路由分析遇到问题，使用默认策略",
            "greeting_help": "请输入具体任务需求，我将为您提供帮助。"
        },
        
        # 意图澄清节点消息
        "intent": {
            "analyzing": "正在分析用户意图...",
            "analyzing_requirements": "正在分析您的需求，确保完全理解您的需求。",
            "analyzing_reply": "正在分析您的回复意图...",
            "confirmed": "意图已确认，将为您制定详细的执行计划",
            "supplement_understood": "我理解您提供的补充信息，让我重新分析您的需求。",
            "requirements_clear": "需求已明确，将为您制定详细的执行计划",
            "need_more_info": "请提供更多详细信息，以便我为您制定最佳的分析方案。",
            "confirmation_thanks": "感谢您的确认！我现在开始为您制定执行计划。",
            "planning_start": "需求已明确，开始制定执行计划。"
        },
        
        # 计划节点消息
        "planning": {
            "creating_plan": "正在为您制定详细的执行计划。",
            "generating": "正在制定执行计划...",
            "analyzing_feedback": "正在分析您的反馈意图...",
            "plan_confirmed": "计划已确认，将开始执行分析任务",
            "modification_understood": "我理解您的修改建议，让我重新制定计划。",
            "generating_execution_plan": "正在生成执行计划...",
            "confirm_plan": "请确认执行计划，如需修改请告诉我具体调整建议。",
            "execution_thanks": "感谢您的确认！我现在开始执行分析任务。"
        },
        
        # 执行节点消息
        "execution": {
            "starting": "开始执行分析任务，请稍候...",
            "executing_plan": "正在执行分析计划...",
            "executing_step": "开始执行{step_name}...",
            "step_start": "接下来我将执行第{step_index}个任务：{step_title}",
            "step_completed": "第{step_index}个任务已完成：{result}",
            "all_completed": "计划已执行完成",
            "background_processing": "任务后台执行中，执行完可在页面查看报告，请稍等..."
        },
        
        # 报告节点消息
        "report": {
            "generating": "正在生成品牌舆情分析报告...",
            "calling_service": "正在调用外部报告服务...",
            "uploading": "正在上传HTML报告到云存储...",
            "parsing_summary": "正在解析报告中的AI总结和关键数据...",
            "generation_failed": "报告生成失败：{error}",
            "completed": "品牌舆情分析报告已生成完成！",
            "ai_summary_preview": "📊 AI总结预览: {preview}",
            "file_ready": "下方完整报告文件包含可视化图表和详细分析，请查收。",
            "upload_failed": "⚠️ HTML报告上传失败: {message}",
            "manual_download": "📄 HTML内容已保存在响应中，可手动下载。",
            "complete_report": "完整报告包含HTML可视化图表和详细分析。",
            "other_help": "还有其他可以帮助您的吗？"
        },
        
        # 错误消息
        "errors": {
            "no_execution_plan": "未提供执行计划",
            "invalid_execution_plan": "执行计划无效",
            "step_failed": "执行失败：{error}",
            "workflow_failed": "工作流执行失败",
            "dsl_generation_failed": "报表DSL生成失败：{error}"
        }
    },
    
    "en_US": {
        # Supervisor node messages
        "supervisor": {
            "analyzing_state": "Analyzing current state and user messages...",
            "analyzing_message_type": "Analyzing user message type and intent...",
            "routing_error": "Routing analysis encountered issues, using default strategy",
            "greeting_help": "Please enter specific task requirements, I will provide assistance."
        },
        
        # Intent clarification node messages
        "intent": {
            "analyzing": "Analyzing user intent...",
            "analyzing_requirements": "Analyzing your requirements to ensure complete understanding.",
            "analyzing_reply": "Analyzing your reply intent...",
            "confirmed": "Intent confirmed, will create detailed execution plan for you",
            "supplement_understood": "I understand the additional information you provided, let me re-analyze your requirements.",
            "requirements_clear": "Requirements are clear, will create detailed execution plan for you",
            "need_more_info": "Please provide more detailed information so I can create the best analysis plan for you.",
            "confirmation_thanks": "Thank you for your confirmation! I will now start creating the execution plan for you.",
            "planning_start": "Requirements are clear, starting to create execution plan."
        },
        
        # Planning node messages
        "planning": {
            "creating_plan": "Creating detailed execution plan for you.",
            "generating": "Generating execution plan...",
            "analyzing_feedback": "Analyzing your feedback intent...",
            "plan_confirmed": "Plan confirmed, will start executing analysis tasks",
            "modification_understood": "I understand your modification suggestions, let me recreate the plan.",
            "generating_execution_plan": "Generating execution plan...",
            "confirm_plan": "Please confirm the execution plan. If modifications are needed, please tell me specific adjustment suggestions.",
            "execution_thanks": "Thank you for your confirmation! I will now start executing the analysis tasks."
        },
        
        # Execution node messages
        "execution": {
            "starting": "Starting to execute analysis tasks, please wait...",
            "executing_plan": "Executing analysis plan...",
            "executing_step": "Starting to execute {step_name}...",
            "step_start": "Next I will execute task {step_index}: {step_title}",
            "step_completed": "Task {step_index} completed: {result}",
            "all_completed": "Plan execution completed",
            "background_processing": "Tasks are running in background, you can view the report on the page when completed, please wait..."
        },
        
        # Report node messages
        "report": {
            "generating": "Generating brand sentiment analysis report...",
            "calling_service": "Calling external report service...",
            "uploading": "Uploading HTML report to cloud storage...",
            "parsing_summary": "Parsing AI summary and key data from report...",
            "generation_failed": "Report generation failed: {error}",
            "completed": "Brand sentiment analysis report generated successfully!",
            "ai_summary_preview": "📊 AI Summary Preview: {preview}",
            "file_ready": "Complete report file with visualizations and detailed analysis is ready below.",
            "upload_failed": "⚠️ HTML report upload failed: {message}",
            "manual_download": "📄 HTML content saved in response, can be downloaded manually.",
            "complete_report": "Complete report contains HTML visualizations and detailed analysis.",
            "other_help": "Is there anything else I can help you with?"
        },
        
        # Error messages
        "errors": {
            "no_execution_plan": "No execution plan provided",
            "invalid_execution_plan": "Invalid execution plan",
            "step_failed": "Execution failed: {error}",
            "workflow_failed": "Workflow execution failed",
            "dsl_generation_failed": "Report DSL generation failed: {error}"
        }
    }
}


class MessageManager:
    """消息管理器"""
    
    def __init__(self, language: str = None):
        self.language = language or os.getenv("WORKFLOW_LANGUAGE", DEFAULT_LANGUAGE)
    
    def get_message(self, key: str, **kwargs) -> str:
        """
        获取消息
        
        Args:
            key: 消息键，支持点分隔的嵌套键，如 "supervisor.analyzing_state"
            **kwargs: 消息参数，用于格式化消息
            
        Returns:
            格式化后的消息文本
        """
        try:
            # 分割键路径
            keys = key.split(".")
            
            # 获取语言配置
            messages = MESSAGES.get(self.language, MESSAGES[DEFAULT_LANGUAGE])
            
            # 逐级获取消息
            current = messages
            for k in keys:
                current = current[k]
            
            # 如果是字符串，进行参数格式化
            if isinstance(current, str):
                return current.format(**kwargs)
            else:
                return str(current)
                
        except (KeyError, TypeError, ValueError) as e:
            # 如果找不到消息或格式化失败，返回键本身作为fallback
            return f"[{key}]"
    
    def agent_message(self, key: str, **kwargs) -> Dict[str, str]:
        """
        生成agent消息格式
        
        Args:
            key: 消息键
            **kwargs: 消息参数
            
        Returns:
            agent消息字典
        """
        return {"agent_message": self.get_message(key, **kwargs)}
    
    def live_status_message(self, key: str, **kwargs) -> Dict[str, str]:
        """
        生成实时状态消息格式
        
        Args:
            key: 消息键
            **kwargs: 消息参数
            
        Returns:
            实时状态消息字典
        """
        return {"live_status_message": self.get_message(key, **kwargs)}
    
    def human_feedback_message(self, key: str, **kwargs) -> Dict[str, str]:
        """
        生成人工反馈消息格式
        
        Args:
            key: 消息键
            **kwargs: 消息参数
            
        Returns:
            人工反馈消息字典
        """
        return {"human_feedback_message": self.get_message(key, **kwargs)}


# 全局消息管理器实例
_message_manager = MessageManager()


def get_message(key: str, **kwargs) -> str:
    """
    获取消息的便捷函数
    
    Args:
        key: 消息键
        **kwargs: 消息参数
        
    Returns:
        格式化后的消息文本
    """
    return _message_manager.get_message(key, **kwargs)


def agent_message(key: str, **kwargs) -> Dict[str, str]:
    """
    生成agent消息的便捷函数
    
    Args:
        key: 消息键
        **kwargs: 消息参数
        
    Returns:
        agent消息字典
    """
    return _message_manager.agent_message(key, **kwargs)


def live_status_message(key: str, **kwargs) -> Dict[str, str]:
    """
    生成实时状态消息的便捷函数
    
    Args:
        key: 消息键
        **kwargs: 消息参数
        
    Returns:
        实时状态消息字典
    """
    return _message_manager.live_status_message(key, **kwargs)


def human_feedback_message(key: str, **kwargs) -> Dict[str, str]:
    """
    生成人工反馈消息的便捷函数
    
    Args:
        key: 消息键
        **kwargs: 消息参数
        
    Returns:
        人工反馈消息字典
    """
    return _message_manager.human_feedback_message(key, **kwargs)


def set_language(language: str):
    """
    设置全局语言
    
    Args:
        language: 语言代码，如 "zh_CN", "en_US"
    """
    global _message_manager
    _message_manager = MessageManager(language)


# 导出的便捷别名
msg = get_message
agent_msg = agent_message
status_msg = live_status_message
feedback_msg = human_feedback_message
