"""
English message configuration file
"""

MESSAGES = {
    # Supervisor node messages
    "supervisor": {
        "analyzing_state": "Analyzing current state and user messages...",
        "analyzing_message_type": "Analyzing user message type and intent...",
        "routing_error": "Routing analysis encountered issues, using default strategy",
        "greeting_help": "Please enter specific task requirements, I will provide assistance."
    },
    
    # Intent clarification node messages
    "intent": {
        "analyzing": "Analyzing user intent...",
        "analyzing_requirements": "Analyzing your requirements to ensure complete understanding.",
        "analyzing_reply": "Analyzing your reply intent...",
        "confirmed": "Intent confirmed, will create detailed execution plan for you",
        "supplement_understood": "I understand the additional information you provided, let me re-analyze your requirements.",
        "requirements_clear": "Requirements are clear, will create detailed execution plan for you",
        "need_more_info": "Please provide more detailed information so I can create the best analysis plan for you.",
        "confirmation_thanks": "Thank you for your confirmation! I will now start creating the execution plan for you.",
        "planning_start": "Requirements are clear, starting to create execution plan."
    },
    
    # Planning node messages
    "planning": {
        "creating_plan": "Creating detailed execution plan for you.",
        "generating": "Generating execution plan...",
        "analyzing_feedback": "Analyzing your feedback intent...",
        "plan_confirmed": "Plan confirmed, will start executing analysis tasks",
        "modification_understood": "I understand your modification suggestions, let me recreate the plan.",
        "generating_execution_plan": "Generating execution plan...",
        "confirm_plan": "Please confirm the execution plan. If modifications are needed, please tell me specific adjustment suggestions.",
        "execution_thanks": "Thank you for your confirmation! I will now start executing the analysis tasks."
    },
    
    # Execution node messages
    "execution": {
        "starting": "Starting to execute analysis tasks, please wait...",
        "executing_plan": "Executing analysis plan...",
        "executing_step": "Starting to execute {step_name}...",
        "step_start": "Next I will execute task {step_index}: {step_title}",
        "step_completed": "Task {step_index} completed: {result}",
        "all_completed": "Plan execution completed",
        "background_processing": "Tasks are running in background, you can view the report on the page when completed, please wait..."
    },
    
    # Report node messages
    "report": {
        "generating": "Generating brand sentiment analysis report...",
        "calling_service": "Calling external report service...",
        "uploading": "Uploading HTML report to cloud storage...",
        "parsing_summary": "Parsing AI summary and key data from report...",
        "generation_failed": "Report generation failed: {error}",
        "completed": "Brand sentiment analysis report generated successfully!",
        "ai_summary_preview": "📊 AI Summary Preview: {preview}",
        "file_ready": "Complete report file with visualizations and detailed analysis is ready below.",
        "upload_failed": "⚠️ HTML report upload failed: {message}",
        "manual_download": "📄 HTML content saved in response, can be downloaded manually.",
        "complete_report": "Complete report contains HTML visualizations and detailed analysis.",
        "other_help": "Is there anything else I can help you with?"
    },
    
    # Error messages
    "errors": {
        "no_execution_plan": "No execution plan provided",
        "invalid_execution_plan": "Invalid execution plan",
        "step_failed": "Execution failed: {error}",
        "workflow_failed": "Workflow execution failed",
        "dsl_generation_failed": "Report DSL generation failed: {error}"
    },
    
    # Status messages
    "status": {
        "initializing": "Initializing...",
        "processing": "Processing...",
        "completed": "Completed",
        "failed": "Execution failed"
    }
}
