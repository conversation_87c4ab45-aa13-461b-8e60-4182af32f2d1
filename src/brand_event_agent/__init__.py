"""
Brand Event Agent - Multi-agent system for brand sentiment analysis.

This package provides a refactored version of the Specific project following
open_deep_research workflow directory structure while maintaining complete
compatibility with existing Agent implementations.
"""

__version__ = "1.0.0"

from .workflow import workflow, run_workflow, stream_workflow
from .state import BrandEventState, WorkflowStatus, create_initial_state
from .config import BrandEventConfig
from .api import BrandEventAPI, BrandEventRequest, BrandEventResponse

__all__ = [
    "workflow",
    "run_workflow",
    "stream_workflow",
    "BrandEventState",
    "WorkflowStatus",
    "create_initial_state",
    "BrandEventConfig",
    "BrandEventAPI",
    "BrandEventRequest",
    "BrandEventResponse"
]
