"""
Configuration management for brand event analysis workflow.

This module provides configuration classes following open_deep_research patterns.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, fields
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from pydantic import SecretStr

@dataclass(kw_only=True)
class WorkflowConfiguration:
    """Configuration for brand event analysis workflow."""
    
    # Model configuration
    supervisor_model: str = "gpt-4o-mini"
    analyst_model: str = "gpt-4o-mini"
    planner_model: str = "gpt-4o-mini"
    executor_model: str = "gpt-4o-mini"
    summarizer_model: str = "gpt-4o-mini"
    reporter_model: str = "gpt-4o-mini"
    
    # API configuration
    openai_api_key: str = "test"
    openai_base_url: str = "https://llm-model-proxy.dev.fc.chj.cloud/agentops"
    
    # Report service configuration
    report_service_url: str = "https://console-playground.fed.chehejia.com"
    upload_service_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    report_timeout: int = 300
    
    # MCP configuration
    mcp_server_url: str = "http://172.21.65.95:5003/mcp/marketing"
    mcp_timeout: int = 300
    mcp_server_config: Optional[Dict[str, Any]] = None
    mcp_prompt: Optional[str] = None
    mcp_tools_to_include: Optional[list[str]] = None
    
    # Workflow configuration
    max_iterations: int = 10
    timeout_seconds: int = 300
    max_planning_rounds: int = 3
    max_clarification_rounds: int = 3
    
    # Logging configuration
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Security configuration
    jwt_secret_key: str = "brand_event"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 2880  # 48 hours
    
    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "WorkflowConfiguration":
        """Create a BrandEventConfig instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})

    def create_llm(self, model_name: str, temperature: float = 0.0) -> ChatOpenAI:
        """
        Create a ChatOpenAI instance with configured base_url and api_key.

        Args:
            model_name: The model name to use (e.g., 'azure-gpt-4o-mini')
            temperature: Temperature for the model (default: 0.0)

        Returns:
            ChatOpenAI instance configured with the workflow settings

        Example:
            config = WorkflowConfiguration()
            llm = config.create_llm('azure-gpt-4o-mini')
        """
        return ChatOpenAI(
            base_url=self.openai_base_url,
            model=model_name,
            api_key=SecretStr(self.openai_api_key),
            temperature=temperature,
        )

    def get_supervisor_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for supervisor model."""
        return self.create_llm(self.supervisor_model, temperature)

    def get_analyst_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for analyst model."""
        return self.create_llm(self.analyst_model, temperature)

    def get_planner_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for planner model."""
        return self.create_llm(self.planner_model, temperature)

    def get_executor_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for executor model."""
        return self.create_llm(self.executor_model, temperature)

    def get_summarizer_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for summarizer model."""
        return self.create_llm(self.summarizer_model, temperature)

    def get_reporter_llm(self, temperature: float = 0.0) -> ChatOpenAI:
        """Get ChatOpenAI instance for reporter model."""
        return self.create_llm(self.reporter_model, temperature)

    def create_mcp_server_config(
        self,
        user_id: str,
        session_id: Optional[str] = None,
        task_id: Optional[str] = None,
        sandbox_id: Optional[str] = None,
        jwt_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create MCP server configuration with filtered headers.

        Args:
            user_id: User account ID
            session_id: Session ID (optional)
            task_id: Task ID (optional)
            sandbox_id: Sandbox ID (optional)
            jwt_token: JWT token for authentication (optional)

        Returns:
            MCP server configuration dictionary

        Example:
            config = WorkflowConfiguration()
            mcp_config = config.create_mcp_server_config(
                user_id="<EMAIL>",
                session_id="session_123",
                jwt_token="jwt_token_here"
            )
        """
        # Use custom config if provided, otherwise use default
        if self.mcp_server_config:
            return self.mcp_server_config

        # Build headers, filtering out None values
        headers = {
            "account": user_id,
        }

        # Add JWT token if provided
        if jwt_token:
            headers["sign"] = jwt_token

        # Only add non-None header values
        if session_id:
            headers["sessionId"] = session_id
        if task_id:
            headers["taskId"] = task_id
        if sandbox_id:
            headers["sandbox_id"] = sandbox_id

        # Create default server configuration
        server_config = {
            "brand_event": {
                "transport": "streamable_http",
                "url": self.mcp_server_url,
                "headers": headers
            }
        }

        return server_config

    def get_mcp_prompt(self) -> Optional[str]:
        """Get MCP prompt if configured."""
        return self.mcp_prompt

    def get_mcp_tools_to_include(self) -> Optional[list[str]]:
        """Get list of MCP tools to include if configured."""
        return self.mcp_tools_to_include
