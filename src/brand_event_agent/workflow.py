"""
Brand event analysis workflow implementation.

This module provides workflow nodes and graph construction
"""

from typing import Dict, Any, Literal, Optional, List
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command, StreamWriter
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import Chat<PERSON>penAI
from pydantic import BaseModel, Field

from .state import WorkflowState, WorkflowStatus
from .config import WorkflowConfiguration
from .prompts import (
    SUPERVISOR_SYSTEM_PROMPT,
    build_supervisor_routing_prompt,
    INTENT_ANALYSIS_SYSTEM_PROMPT,
    build_intent_analysis_prompt,
    build_clarification_prompt,
    PLANNING_SYSTEM_PROMPT,
    build_planning_prompt,
    build_planning_intent_analysis_prompt,
    EXECUTION_SYSTEM_PROMPT,
    REPORTING_SYSTEM_PROMPT
)
from .utils import (
    get_logger, get_latest_user_response, get_latest_plan_feedback, get_clarified_requirements,
    format_plan_message, create_access_token, generate_report, upload_html_to_s3,
    parse_dsl_summary, create_brand_analysis_dsl
)

# Import additional dependencies for execution and report nodes
import datetime
import jwt
import httpx
import tempfile
import os
from agentops_event_sdk_python import ChatAttachment

logger = get_logger("BrandEventWorkflow")


# ==================== Data Models ====================
# Extracted from existing Agent implementations

class UserMessageAnalysis(BaseModel):
    """用户消息分析结果"""
    message_type: Literal[
        "greeting", "task", "supplement", "agreement", "rejection", "detailed_description", "short_reply"] = Field(
        description="消息类型：greeting(问候), task(任务请求), supplement(补充信息), agreement(确认同意), rejection(拒绝否定)"
    )
    user_intent: str = Field(description="用户真实意图分析")
    extracted_info: str = Field(default="", description="从用户消息中提取的关键信息")


class RouterDecisionWithAnalysis(BaseModel):
    """路由决策结果（包含分析）"""
    message_analysis: UserMessageAnalysis = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "report", "__end__"] = Field(
        description="下一步路由目标"
    )
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="需要返回给用户的消息（仅问候时填写）")
    workflow_status: str = Field(description="更新后的工作流状态")


class UserIntentAnalysis(BaseModel):
    """用户意图分析结果"""
    intent_type: Literal["agreement", "supplement", "rejection"] = Field(description="意图类型")
    next_action: Literal["continue", "retry"] = Field(description="下一步行动")
    extracted_info: str = Field(default="", description="提取的补充信息")


class IntentClarificationResult(BaseModel):
    """意图澄清结果"""
    intent_clear: bool = Field(description="意图是否清晰")
    clarification_questions: List[str] = Field(default=[], description="澄清问题列表")
    clarification_result: Optional[Dict[str, Any]] = Field(default=None, description="澄清结果")
    summary: str = Field(description="分析总结")
    response_message: str = Field(description="回复消息")


class PlanStep(BaseModel):
    """计划步骤"""
    title: str = Field(description="步骤标题")
    description: str = Field(description="步骤描述")
    estimated_time: str = Field(default="", description="预估时间")
    skip_execute: bool = Field(default=False, description="是否跳过执行")


class ExecutionPlan(BaseModel):
    """执行计划 - 与ExecutionAgent保持一致"""
    title: str = Field(description="计划标题")
    objective: str = Field(description="计划目标")
    steps: List[PlanStep] = Field(description="执行步骤")
    estimated_time: str = Field(default="", description="总预估时间")


class Plan(BaseModel):
    """执行计划"""
    title: str = Field(description="计划标题")
    objective: str = Field(description="计划目标")
    steps: List[PlanStep] = Field(description="执行步骤")
    estimated_time: str = Field(default="", description="总预估时间")


# JWT配置常量（与ExecutionAgent保持一致）
SECRET_KEY = "brand_event"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 2880  # 48小时


# ==================== Supervisor Node ====================

async def supervisor_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Supervisor node - 完全移植SupervisorAgent.execute逻辑
    """
    configurable = WorkflowConfiguration.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing workflow supervision, session_id:{session_id}")

    try:
        # 发送监督开始的消息
        writer({"live_status_message": "正在分析当前状态和用户消息..."})

        # 使用LLM进行智能路由决策
        result = await _llm_based_routing(state, session_id, writer, configurable)
        route_reason = result.update.get('reason', 'N/A')

        logger.info(
            f"Completed workflow supervision, result: route to {result.goto}, reason: {route_reason}, session_id:{session_id}")
        return result

    except Exception as e:
        logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")

        # 发送错误分析消息
        writer({"live_status_message": "路由分析遇到问题，使用默认策略"})

        # 出错时默认路由到意图澄清
        return Command(goto="intent_clarification", update={"workflow_status": WorkflowStatus.CLARIFYING_INTENT})


async def _llm_based_routing(state: WorkflowState, session_id: str,  writer: StreamWriter, config: WorkflowConfiguration) -> Command:
    """
    基于LLM的智能路由决策
    """
    try:
        # 构建路由决策提示词
        routing_prompt = build_supervisor_routing_prompt(state)

        # 发送LLM分析的消息
        writer({"live_status_message": "正在分析用户消息类型和意图..."})

        # 初始化LLM
        supervisor_model = config.supervisor_model
        llm = ChatOpenAI(
            model=supervisor_model,
            temperature=0
        )

        # 使用结构化输出获取路由决策
        response = llm.with_structured_output(RouterDecisionWithAnalysis).invoke( [HumanMessage(content=routing_prompt)])

        # 记录分析结果
        logger.info(f"User message analysis: type={response.message_analysis.message_type}, session_id:{session_id}")
        logger.info(f"LLM routing decision: {response.next}, reason: {response.reason}, session_id:{session_id}")

        # 直接根据LLM返回的结构化结果构建Command
        update_data = {
            "reason": response.reason,
            "workflow_status": response.workflow_status
        }

        # 如果是问候消息，添加回复消息
        if response.message_analysis.message_type == "greeting" and response.next == '__end__' and response.response_message:
            writer({"agent_message": response.response_message})
            writer({"human_feedback_message": "请输入具体任务需求，我将为您提供帮助。"})

        return Command(goto=response.next, update=update_data)

    except Exception as e:
        logger.error(f"Failed LLM-based routing, error: {str(e)}, session_id:{session_id}")

        # 发送错误分析消息
        writer({"live_status_message": "路由分析遇到问题，使用默认策略"})

        # 出错时默认路由到意图澄清
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.INITIALIZING})


# ==================== Intent Clarification Node ====================

async def intent_clarification_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Intent clarification node
    """
    configurable = WorkflowConfiguration.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    clarification_round = state.get("clarification_round", 0)
    logger.info(f"Start doing intent clarification (round {clarification_round + 1}), session_id:{session_id}")

    try:
        

        # 发送任务开始的Agent消息
        writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})

        # 发送实时状态消息
        writer({"live_status_message": "正在分析用户意图..."})

        # 获取基本信息
        latest_user_response = get_latest_user_response(state)
        context_messages = state.get("messages", [])

        # 初始化LLM
        analyst_model = configurable.analyst_model
        llm = ChatOpenAI(
            model=analyst_model,
            temperature=0
        )

        # 如果是多轮澄清，先分析用户意图
        if clarification_round > 0 and latest_user_response:
            # 发送分析意图的状态消息
            writer({"live_status_message": "正在分析您的回复意图..."})

            intent_analysis = await _analyze_user_intent(
                llm=llm,
                user_response=latest_user_response,
                current_stage="意图澄清阶段",
                context_messages=context_messages
            )

            # 根据意图分析结果处理
            if intent_analysis.intent_type == "agreement":
                # 发送用户确认的消息
                writer({"agent_message": "意图已确认，将为您制定详细的执行计划"})

                logger.info(
                    f"Completed intent clarification, result: user approved requirements, session_id:{session_id}")
                return Command(
                    goto="planning",
                    update={
                        "intent_clarified": True,
                        "intent_approved": True,  # 用户确认审批通过
                        "workflow_status": WorkflowStatus.PLANNING,  # 审批通过，进入计划阶段
                        "messages": state["messages"] + [
                            HumanMessage(content="感谢您的确认！我现在开始为您制定执行计划。")]
                    }
                )

            elif intent_analysis.intent_type == "supplement":
                # 发送补充信息的消息
                writer({"agent_message": "我理解您提供的补充信息，让我重新分析您的需求。"})

                logger.info(f"User provided supplement info, re-analyzing intent, session_id:{session_id}")
                # 继续进行澄清分析

        # 判断用户意图是否清晰
        user_input = state.get("user_input", "")
        clarification_result = await _is_intent_clear(
            llm=llm,
            user_input=user_input,
            context_messages=context_messages
        )

        if clarification_result.intent_clear:
            # 意图清晰，直接进入计划阶段
            writer({"agent_message": clarification_result.response_message})
            writer({"agent_message": "需求已明确，将为您制定详细的执行计划"})

            logger.info(f"Completed intent clarification, result: intent is clear, session_id:{session_id}")
            return Command(
                goto="planning",
                update={
                    "intent_clarified": True,
                    "intent_approved": True,
                    "clarification_result": clarification_result.clarification_result,
                    "workflow_status": WorkflowStatus.PLANNING,
                    "messages": state["messages"] + [
                        HumanMessage(content=clarification_result.response_message),
                        HumanMessage(content="需求已明确，开始制定执行计划。")
                    ],
                    "clarification_round": clarification_round + 1
                }
            )
        else:
            # 需要澄清，发送澄清问题
            writer({"agent_message": clarification_result.response_message})
            writer({"human_feedback_message": "请提供更多详细信息，以便我为您制定最佳的分析方案。"})

            logger.info(f"Completed intent clarification, result: need clarification, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "intent_clarified": False,
                    "intent_approved": False,
                    "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
                    "messages": state["messages"] + [HumanMessage(content=clarification_result.response_message)],
                    "clarification_round": clarification_round + 1
                }
            )

    except Exception as e:
        logger.error(f"Failed intent clarification, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})


# ==================== Helper Functions ====================
# 完全移植现有Agent的辅助函数

async def _analyze_user_intent(llm, user_response: str, current_stage: str, context_messages) -> UserIntentAnalysis:
    """
    分析用户意图 - 完全移植IntentAnalysisAgent._analyze_user_intent逻辑
    """
    try:
        # 构建意图分析提示词
        intent_prompt = build_intent_analysis_prompt(current_stage, user_response)

        # 使用结构化输出分析用户意图
        messages = [
            SystemMessage(content=INTENT_ANALYSIS_SYSTEM_PROMPT),
            HumanMessage(content=intent_prompt)
        ]

        response = llm.with_structured_output(UserIntentAnalysis).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze user intent: {e}")
        # 返回默认的补充信息意图
        return UserIntentAnalysis(
            intent_type="supplement",
            next_action="retry",
            extracted_info=user_response
        )


async def _is_intent_clear(llm, user_input: str, context_messages) -> IntentClarificationResult:
    """
    判断用户意图是否清晰 - 完全移植IntentAnalysisAgent._is_intent_clear逻辑
    """
    try:
        # 构建澄清分析提示词
        clarification_prompt = build_clarification_prompt(user_input)

        # 使用结构化输出分析意图清晰度
        messages = [
            SystemMessage(content=INTENT_ANALYSIS_SYSTEM_PROMPT),
            HumanMessage(content=clarification_prompt)
        ]

        response = llm.with_structured_output(IntentClarificationResult).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze intent clarity: {e}")
        # 返回默认的需要澄清结果
        return IntentClarificationResult(
            intent_clear=False,
            clarification_questions=["请提供更多详细信息"],
            clarification_result=None,
            summary="分析过程中出现错误",
            response_message="为了更好地为您服务，请提供更多详细信息。"
        )


# ==================== Planning Node ====================

async def planning_node(state: WorkflowState, writer: StreamWriter,  config: RunnableConfig) -> Command:
    """
    Planning node - 完全移植PlanningAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    planning_round = state.get("planning_round", 0)
    logger.info(f"Start doing planning (round {planning_round + 1}), session_id:{session_id}")

    try:


        # 发送任务开始的Agent消息
        writer({"agent_message": "正在为您制定详细的执行计划。"})

        # 发送实时状态消息
        writer({"live_status_message": "正在制定执行计划..."})

        # 获取基本信息
        latest_plan_feedback = get_latest_plan_feedback(state)

        # 初始化LLM
        configurable = WorkflowConfiguration.from_runnable_config(config)
        llm = ChatOpenAI(
            model=configurable.planner_model,
            temperature=0
        )

        # 如果有用户反馈且计划未批准，先分析反馈意图
        if latest_plan_feedback and not state.get("plan_approved", False):
            # 发送分析反馈的状态消息
            writer({"live_status_message": "正在分析您的反馈意图..."})

            feedback_analysis = await _analyze_plan_feedback_intent(
                llm=llm,
                feedback=latest_plan_feedback
            )

            # 根据反馈分析结果处理
            if feedback_analysis.intent_type == "agreement":
                # 发送用户确认的消息
                writer({"agent_message": "计划已确认，将开始执行分析任务"})

                logger.info(f"Completed planning, result: user approved plan, session_id:{session_id}")
                return Command(
                    goto="execution",
                    update={
                        "plan_approved": True,
                        "workflow_status": WorkflowStatus.EXECUTING,
                        "messages": state["messages"] + [HumanMessage(content="感谢您的确认！我现在开始执行分析任务。")]
                    }
                )

            elif feedback_analysis.intent_type == "supplement":
                # 发送修改计划的消息
                writer({"agent_message": "我理解您的修改建议，让我重新制定计划。"})

                logger.info(f"User provided plan modification suggestions, re-planning, session_id:{session_id}")
                # 继续进行计划制定

        # 发送制定计划的状态消息
        writer({"live_status_message": "正在生成执行计划..."})

        # 制定执行计划
        clarified_requirements = get_clarified_requirements(state)
        previous_plan = state.get("task_plan")

        execution_plan = await _create_execution_plan(
            llm=llm,
            user_requirements=clarified_requirements,
            previous_plan=previous_plan
        )

        # 格式化计划消息
        is_revision = planning_round > 0
        plan_message = format_plan_message(execution_plan.dict(), is_revision)

        # 发送计划消息
        writer({"agent_message": plan_message})
        writer({"human_feedback_message": "请确认执行计划，如需修改请告诉我具体调整建议。"})

        logger.info(f"Completed planning, result: plan created, session_id:{session_id}")
        return Command(
            goto="__end__",
            update={
                "task_plan": execution_plan.model_dump(),
                "plan_approved": False,  # 等待用户确认
                "workflow_status": WorkflowStatus.PLANNING,
                "messages": state["messages"] + [HumanMessage(content=plan_message)],
                "planning_round": planning_round + 1
            }
        )

    except Exception as e:
        logger.error(f"Failed planning, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})


# ==================== Execution Node ====================

def _create_access_token(user_id: str) -> str:
    """
    生成JWT Token（与ExecutionAgent保持一致）
    """
    expire = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        minutes=ACCESS_TOKEN_EXPIRE_MINUTES
    )

    to_encode = {
        "sub": user_id,
        "exp": expire,
        "iat": datetime.datetime.now(datetime.timezone.utc),
        "type": "access"
    }

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def _get_execution_agent(state: WorkflowState, configurable: WorkflowConfiguration):
    """获取执行agent，每次使用时获取 - 完全移植ExecutionAgent._get_agent逻辑"""
    try:
        # 尝试创建React Agent with MCP tools
        from langchain_mcp_adapters.client import MultiServerMCPClient
        from langgraph.prebuilt import create_react_agent

        # 从状态中获取用户信息
        user_id = '<EMAIL>'  # 默认用户ID
        session_id = state.get('session_id')
        task_id = state.get('task_id')
        sandbox_id = state.get('sandbox_id')

        # 动态生成JWT token
        dynamic_token = _create_access_token(user_id)
        logger.info(f"Generated dynamic JWT token for user: {user_id}")

        # MCP服务器配置
        server_configs = {
            "brand_event": {
                "transport": "streamable_http",
                "url": "http://172.21.65.95:5003/mcp/marketing",
                "enabled_tools": [],
                "add_to_agents": [
                    "researcher",
                    "coder"
                ],
                "headers": {
                    "sessionId": session_id,
                    "taskId": task_id,
                    "sandbox_id": sandbox_id,
                    "account": user_id,
                    "sign": dynamic_token,
                }
            }
        }

        # 创建MCP客户端
        mcp_client = MultiServerMCPClient(server_configs)

        # 获取MCP工具
        tools = await mcp_client.get_tools()

        if tools:
            # 创建React agent with MCP tools
            llm = ChatOpenAI(
                model=configurable.executor_model,
                temperature=0
            )
            agent = create_react_agent(llm, tools)
            logger.info(f"Created React agent with {len(tools)} MCP tools")
            return agent
        else:
            # 没有工具时使用普通LLM
            logger.warning("No MCP tools available, using plain LLM")
            return ChatOpenAI(
                model=configurable.executor_model,
                temperature=0
            )

    except Exception as e:
        logger.error(f"Failed to setup React agent: {e}")
        # Fallback到普通LLM
        return ChatOpenAI(
            model=configurable.executor_model,
            temperature=0
        )


async def _execute_single_step(
        step_index: int,
        plan_step: PlanStep,
        writer: StreamWriter,
        state: WorkflowState,
        agent
) -> str:
    """
    执行单个步骤 - 完全移植ExecutionAgent.execute_step逻辑
    """
    try:
        logger.info(f"Executing step {step_index}: {plan_step.title}")

        # 发送步骤开始前的Agent消息
        writer({"agent_message": f"接下来我将执行第{step_index + 1}个任务：{plan_step.title}"})

        # 直接执行步骤
        try:
            step_result = await _execute_step_content(plan_step, agent, state)
        except Exception as e:
            logger.error(f"Step {plan_step.title} failed: {e}")
            step_result = f"❌ {plan_step.title}: 执行失败 - {str(e)}"

        # 发送步骤完成后的Agent消息
        writer({"agent_message": f"第{step_index + 1}个任务已完成：{step_result}"})

        return step_result

    except Exception as e:
        logger.error(f"Error executing step {step_index}: {e}")
        return f"执行步骤 {step_index + 1} 时出现错误：{str(e)}"


async def _execute_step_content(plan_step: PlanStep, agent, state: WorkflowState) -> str:
    """
    执行步骤内容 - 完全移植ExecutionAgent._execute_step逻辑
    """
    try:
        # 构建简化的agent输入
        agent_input = {
            "messages": [
                HumanMessage(
                    content=f"用户问题为: {state.get('intent_summary')}，请原样传递给工具"
                )
            ]
        }

        # 打印请求数据
        logger.info("=" * 60)
        logger.info(f"🚀 执行步骤请求数据: {plan_step.title}")
        for m in agent_input["messages"]:
            m.pretty_print()
        logger.info("=" * 60)

        # 使用agent执行步骤
        response = await agent.ainvoke(agent_input)

        # 打印返回数据
        logger.info("=" * 60)
        logger.info(f"📥 执行步骤返回数据:")

        if isinstance(response, dict) and "messages" in response:
            for m in response["messages"]:
                m.pretty_print()
            result_content = response["messages"][-1].content
            logger.info("=" * 60)
            return result_content
        else:
            logger.info(f"直接返回: {str(response)[:200]}...")
            logger.info("=" * 60)
            return str(response)

    except Exception as e:
        logger.error(f"Error executing step {plan_step.title}: {e}")
        raise e


async def execution_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Execution node - 完全移植ExecutionAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    task_plan_dict = state.get("task_plan", {})

    try:
        # 将字典转换为ExecutionPlan结构体
        if not task_plan_dict:
            logger.error(f"Failed task execution, error: no execution plan provided, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "error_info": {"message": "未提供执行计划", "node": "execution"},
                    "workflow_status": WorkflowStatus.FAILED
                }
            )

        execution_plan = ExecutionPlan(**task_plan_dict)

        # 验证执行计划
        if not (execution_plan and execution_plan.title and execution_plan.steps and len(execution_plan.steps) > 0):
            logger.error(f"Failed task execution, error: invalid execution plan, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "error_info": {"message": "执行计划无效", "node": "execution"},
                    "workflow_status": WorkflowStatus.FAILED
                }
            )

        total_steps = len(execution_plan.steps)
        logger.info(f"Start doing task execution ({total_steps} steps), session_id:{session_id}")

        # 获取配置和agent
        configurable = WorkflowConfiguration.from_runnable_config(config)
        agent = await _get_execution_agent(state, configurable)

        # 遍历并执行所有步骤
        step_reports = []
        for step_index, plan_step in enumerate(execution_plan.steps):
            if plan_step.skip_execute:
                logger.info(f"Skipping step {step_index + 1}: {plan_step.title}")
                continue
            # 发送实时状态消息 - 开始执行
            writer({"live_status_message": f"开始执行{plan_step.title}..."})
            try:
                step_report = await _execute_single_step(step_index, plan_step, writer, state, agent)
                step_reports.append(step_report)
            except Exception as e:
                logger.error(f"Step {step_index + 1} failed: {e}")
                step_reports.append(f"### 步骤{step_index + 1}：{plan_step.title}\n❌ 执行失败：{str(e)}")

        # 生成执行报告
        execution_report = f"## {execution_plan.title} - 执行报告\n\n" + "\n\n".join(step_reports)

        # 发送完成消息
        writer({"agent_message": "计划已执行完成"})
        writer({"agent_status_waiting": "任务后台执行中，执行完可在页面查看报告，请稍等..."})
        logger.info(f"Execution completed, session_id:{session_id}")

        return Command(
            goto="report",
            update={
                "workflow_status": WorkflowStatus.REPORT,
                "execution_report": execution_report,
                "messages": state["messages"] + [HumanMessage(content=execution_report)]
            }
        )

    except Exception as e:
        logger.error(f"Execution failed: {e}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})





# ==================== Report Node ====================

def _parse_dsl_summary(report_dsl: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse AI summary and key data from DSL - 完全移植ReportAgent.parse_dsl_summary逻辑
    """
    try:
        summary_data = {
            "ai_summary": "",
            "key_metrics": [],
            "viewpoints": [],
            "charts_info": []
        }

        for section_key, section in report_dsl.items():
            if not isinstance(section, dict):
                continue

            section_title = section.get("title", "")
            section_description = section.get("description", "")
            section_content = section.get("content", [])

            # Extract AI summary
            if "AI总结" in section_title or "总结" in section_title:
                summary_data["ai_summary"] = section_description
                logger.info(f"Found AI summary in section: {section_title}")

            # Extract key metrics
            for content_item in section_content:
                if content_item.get("type") == "descriptions":
                    data_items = content_item.get("data", [])
                    for item in data_items:
                        if isinstance(item, dict) and "label" in item and "value" in item:
                            summary_data["key_metrics"].append({
                                "label": item["label"],
                                "value": item["value"]
                            })

                # Extract viewpoint information
                elif content_item.get("type") == "table":
                    table_data = content_item.get("data", [])
                    for row in table_data:
                        if isinstance(row, dict) and "viewpoint" in row:
                            summary_data["viewpoints"].append({
                                "viewpoint": row.get("viewpoint", ""),
                                "explain": row.get("explain", ""),
                                "positive": row.get("positive", ""),
                                "negative": row.get("negative", "")
                            })

                # Extract chart information
                elif content_item.get("type") in ["pie", "bar", "bar:negative", "table"]:
                    chart_info = {
                        "type": content_item.get("type"),
                        "title": content_item.get("title") or content_item.get("option", {}).get("title", {}).get(
                            "text", section_title)
                    }
                    summary_data["charts_info"].append(chart_info)

        logger.info(f"Parsed DSL summary: AI summary length={len(summary_data['ai_summary'])}, "
                     f"metrics={len(summary_data['key_metrics'])}, "
                     f"viewpoints={len(summary_data['viewpoints'])}")

        return summary_data

    except Exception as e:
        logger.error(f"Error parsing DSL summary: {e}")
        return {
            "ai_summary": "",
            "key_metrics": [],
            "viewpoints": [],
            "charts_info": []
        }


def _generate_text_summary(summary_data: Dict[str, Any]) -> str:
    """
    基于解析的DSL数据生成文本总结 - 完全移植ReportAgent.generate_text_summary逻辑
    """
    try:
        text_summary = "# 品牌舆情分析报告\n\n"

        # AI总结部分
        if summary_data["ai_summary"]:
            text_summary += "## AI智能总结\n\n"
            # 处理换行符，使格式更清晰
            ai_summary = summary_data["ai_summary"].replace("\\n", "\n")
            text_summary += f"{ai_summary}\n\n"

        # 关键指标部分
        if summary_data["key_metrics"]:
            text_summary += "## 关键指标\n\n"
            for metric in summary_data["key_metrics"]:
                text_summary += f"- **{metric['label']}**: {metric['value']}\n"
            text_summary += "\n"

        # 观点分析部分
        if summary_data["viewpoints"]:
            text_summary += "## 观点分析\n\n"
            for i, viewpoint in enumerate(summary_data["viewpoints"], 1):
                text_summary += f"### {i}. {viewpoint['viewpoint']}\n\n"
                if viewpoint['explain']:
                    text_summary += f"**解释说明**: {viewpoint['explain']}\n\n"
                if viewpoint['positive']:
                    text_summary += f"**正向观点**:\n{viewpoint['positive']}\n\n"
                if viewpoint['negative']:
                    text_summary += f"**负向观点**:\n{viewpoint['negative']}\n\n"

        # 图表信息部分
        if summary_data["charts_info"]:
            text_summary += "## 可视化图表\n\n"
            for chart in summary_data["charts_info"]:
                text_summary += f"- **{chart['title']}** ({chart['type']})\n"
            text_summary += "\n"

        text_summary += "---\n\n*本报告基于DSL数据结构自动生成，包含完整的HTML可视化图表。*"

        return text_summary

    except Exception as e:
        logger.error(f"Error generating text summary: {e}")
        return "# 品牌舆情分析报告\n\n报告生成过程中出现错误，请查看HTML版本获取完整内容。"


async def _generate_report_html(report_dsl: Dict[str, Any], configurable: WorkflowConfiguration) -> Dict[str, Any]:
    """
    调用外部报告服务生成报告 - 完全移植ReportAgent.generate_report逻辑
    """
    try:
        logger.info("Starting report generation")

        # 构建请求URL
        base_url = getattr(configurable, 'report_base_url', "https://console-playground.fed.chehejia.com")
        url = f"{base_url}/__ui/report"

        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer api-key:eyJhbGciOiJIUzI1NiJ9.eyJzIjoidWkiLCJpYXQiOjE3NDk2MzI5NDgsImlzcyI6IjdONVg5MHNkOUtuZGlDS1Q1VjlKWGwifQ.yKQahvhNPXjGCfmFHEJVl9iMWqGodm_QJ8GHjKxUuHI",
        }

        # 发送请求
        timeout = getattr(configurable, 'report_timeout', 30)
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url=url,
                headers=headers,
                json=report_dsl
            )

            # 检查响应状态
            if response.status_code == 200:
                # 接口返回HTML格式的报表
                html_content = response.text
                logger.info("Report generated successfully")
                return {
                    "success": True,
                    "html_content": html_content,
                    "content_type": "text/html",
                    "message": "报告生成成功"
                }
            else:
                logger.error(f"Report generation failed: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "message": "报告生成失败"
                }

    except httpx.TimeoutException:
        logger.error("Report generation timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "报告生成超时"
        }
    except Exception as e:
        logger.error(f"Report generation error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"报告生成失败：{str(e)}"
        }


async def _upload_html_to_s3_service(html_content: str, configurable: WorkflowConfiguration, filename: Optional[str] = None) -> Dict[str, Any]:
    """
    将HTML内容上传到S3存储 - 完全移植ReportAgent.upload_html_to_s3逻辑
    """
    try:
        logger.info("Starting HTML upload to S3")

        # 生成文件名
        if not filename:
            timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"report-{timestamp}.html"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(html_content)
            temp_file_path = temp_file.name

        try:
            # 准备上传文件
            with open(temp_file_path, 'rb') as file:
                files = {
                    'file': (filename, file, 'text/html')
                }

                # 发送上传请求
                upload_url = getattr(configurable, 'upload_url', "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload")
                timeout = getattr(configurable, 'report_timeout', 30)
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        url=upload_url,
                        files=files
                    )

                    # 检查响应状态
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"HTML uploaded successfully: {result}")
                        return {
                            "success": True,
                            "upload_result": result,
                            "filename": filename,
                            "message": "HTML报告上传成功"
                        }
                    else:
                        logger.error(f"HTML upload failed: {response.status_code} - {response.text}")
                        return {
                            "success": False,
                            "error": f"HTTP {response.status_code}: {response.text}",
                            "message": "HTML报告上传失败"
                        }

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except httpx.TimeoutException:
        logger.error("HTML upload timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "HTML报告上传超时"
        }
    except Exception as e:
        logger.error(f"HTML upload error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告上传失败：{str(e)}"
        }


def _create_brand_analysis_dsl_default(
        brand_name: str = "京东",
        event_id: str = "100395410300708206",
        analysis_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    创建品牌分析报告DSL - 完全移植ReportAgent.create_brand_analysis_dsl逻辑
    """
    if not analysis_data:
        analysis_data = {
            "sentiment_score": 75,
            "mention_count": 1250,
            "positive_ratio": 0.65,
            "negative_ratio": 0.20,
            "neutral_ratio": 0.15
        }

    return {
        "01": {
            "type": "section",
            "title": "品牌舆情分析概览",
            "description": f"{brand_name}品牌舆情监测分析报告",
            "content": [
                {
                    "type": "descriptions",
                    "data": [
                        {"id": 1, "label": "品牌名称", "value": brand_name},
                        {"id": 2, "label": "分析事件ID", "value": event_id},
                        {"id": 3, "label": "舆情评分", "value": f"{analysis_data.get('sentiment_score', 0)}分"},
                        {"id": 4, "label": "提及次数", "value": f"{analysis_data.get('mention_count', 0)}次"}
                    ]
                }
            ]
        },
        "02": {
            "type": "section",
            "title": "情感分析分布",
            "description": "品牌相关内容的情感倾向分析",
            "content": [
                {
                    "type": "chart",
                    "chart_type": "pie",
                    "data": [
                        {"label": "正面", "value": analysis_data.get('positive_ratio', 0) * 100,
                         "color": "#52c41a"},
                        {"label": "负面", "value": analysis_data.get('negative_ratio', 0) * 100,
                         "color": "#ff4d4f"},
                        {"label": "中性", "value": analysis_data.get('neutral_ratio', 0) * 100, "color": "#faad14"}
                    ]
                }
            ]
        },
        "03": {
            "type": "section",
            "title": "关键指标",
            "description": "品牌舆情关键性能指标",
            "content": [
                {
                    "type": "metrics",
                    "data": [
                        {"label": "舆情健康度", "value": f"{analysis_data.get('sentiment_score', 0)}%",
                         "trend": "up"},
                        {"label": "传播声量", "value": f"{analysis_data.get('mention_count', 0)}", "trend": "up"},
                        {"label": "正面占比", "value": f"{analysis_data.get('positive_ratio', 0) * 100:.1f}%",
                         "trend": "stable"},
                        {"label": "负面占比", "value": f"{analysis_data.get('negative_ratio', 0) * 100:.1f}%",
                         "trend": "down"}
                    ]
                }
            ]
        }
    }


async def report_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Report node - 完全移植ReportAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')

    try:
        logger.info(f"Starting final report generation, session_id:{session_id}")

        # Send start message
        writer({"agent_message": "正在生成品牌舆情分析报告..."})

        # Get report DSL data
        report_dsl_data = state.get("report_dsl_data")
        report_dsl_status = state.get("report_dsl_status")
        report_dsl_message = state.get("report_dsl_message")

        # Check DSL status
        if report_dsl_status == "FAILED":
            error_message = f"报表DSL生成失败：{report_dsl_message or '未知错误'}"
            logger.error(f"Report DSL generation failed: {report_dsl_message}, session_id:{session_id}")

            # Send error message
            writer({"agent_message": error_message})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "error_info": {"message": error_message, "node": "report"}
                }
            )

        # Use default data if no DSL data provided
        if not report_dsl_data:
            logger.info(f"No report DSL data provided, using default brand analysis DSL, session_id:{session_id}")
            report_dsl_data = _create_brand_analysis_dsl_default()

        # Get configuration
        configurable = WorkflowConfiguration.from_runnable_config(config)

        # Call external report service to generate report
        writer({"live_status_message": "正在调用外部报告服务..."})
        result = await _generate_report_html(report_dsl_data, configurable)

        if result["success"]:
            # Report generation successful, get HTML content
            html_content = result["html_content"]

            # Upload HTML to S3
            writer({"live_status_message": "正在上传HTML报告到云存储..."})
            upload_result = await _upload_html_to_s3_service(
                html_content,
                configurable,
                filename=f"report-{session_id}-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.html"
            )

            # Parse AI summary and key data from DSL
            writer({"live_status_message": "正在解析报告中的AI总结和关键数据..."})
            summary_data = _parse_dsl_summary(report_dsl_data)

            # Generate structured text summary based on DSL data
            final_report = _generate_text_summary(summary_data)

            # 准备完成消息
            ai_summary_preview = summary_data["ai_summary"][:100] + "..." if len(
                summary_data["ai_summary"]) > 100 else summary_data["ai_summary"]

            if upload_result["success"]:
                upload_info = upload_result.get("upload_result", {})
                file_name = upload_result.get("filename", "")
                file_key = upload_info.get("fileKey", "")
                completion_message = f"""品牌舆情分析报告已生成完成！

📊 AI总结预览: {ai_summary_preview}



下方完整报告文件包含可视化图表和详细分析，请查收。"""
                writer({"agent_message": completion_message})
                writer(
                    {"agent_message_with_file":
                        {
                            "content": "报告文件",
                            "attachments": [ChatAttachment(
                                file_key=file_key,
                                local_path=file_key,
                                type="file",
                                filename=file_name,
                                content_type="text/html",
                                content_length=len(html_content),
                            )]
                        }
                    }
                )

            else:
                completion_message = f"""品牌舆情分析报告已生成完成！

📊 AI总结预览: {ai_summary_preview}

⚠️ HTML报告上传失败: {upload_result.get('message', '')}
📄 HTML内容已保存在响应中，可手动下载。

完整报告包含HTML可视化图表和详细分析。"""


            logger.info(
                f"Final report generated successfully with DSL parsing and S3 upload, session_id:{session_id}")
            writer({"human_feedback_message": "还有其他可以帮助您的吗？"})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.INITIALIZING,
                    "final_report": final_report,
                    "html_report": html_content,
                    "report_dsl_data": report_dsl_data,
                    "summary_data": summary_data,  # 保存解析后的结构化数据
                    "upload_result": upload_result,  # 保存上传结果
                    "messages": state["messages"] + [HumanMessage(content=final_report)]
                }
            )
        else:
            # 报告生成失败
            error_message = result.get("message", "报告生成失败")
            logger.error(f"Report generation failed: {result.get('error')}, session_id:{session_id}")

            # 发送错误消息
            writer({"agent_message": f"报告生成失败：{error_message}"})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "error_info": {"message": error_message, "node": "report"}
                }
            )

    except Exception as e:
        logger.error(f"Failed to generate report: {e}, session_id:{session_id}")

        # Send error message
        writer({"agent_message": f"报告生成失败：{str(e)}"})

        return Command(
            goto="__end__",
            update={
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": f"报告生成失败：{str(e)}", "node": "report"}
            }
        )


# ==================== Planning Helper Functions ====================

async def _analyze_plan_feedback_intent(llm, feedback: str) -> UserIntentAnalysis:
    """
    分析计划反馈意图 - 完全移植PlanningAgent._analyze_plan_feedback_intent逻辑
    """
    try:
        # 构建反馈分析提示词
        feedback_prompt = build_planning_intent_analysis_prompt(feedback)

        # 使用结构化输出分析反馈意图
        messages = [
            SystemMessage(content=PLANNING_SYSTEM_PROMPT),
            HumanMessage(content=feedback_prompt)
        ]

        response = llm.with_structured_output(UserIntentAnalysis).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze plan feedback intent: {e}")
        # 返回默认的补充信息意图
        return UserIntentAnalysis(
            intent_type="supplement",
            next_action="retry",
            extracted_info=feedback
        )


async def _create_execution_plan(llm, user_requirements: str, previous_plan=None) -> Plan:
    """
    创建执行计划 - 完全移植PlanningAgent._create_execution_plan逻辑
    """
    try:
        # 构建规划提示词
        planning_prompt = build_planning_prompt(user_requirements, previous_plan)

        # 使用结构化输出生成执行计划
        messages = [
            SystemMessage(content=PLANNING_SYSTEM_PROMPT),
            HumanMessage(content=planning_prompt)
        ]

        response = llm.with_structured_output(Plan).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to create execution plan: {e}")
        # 返回默认计划
        return Plan(
            title="品牌舆情分析计划",
            objective="分析品牌舆情状况",
            steps=[
                PlanStep(
                    title="数据收集",
                    description="收集品牌相关舆情数据",
                    estimated_time="30分钟"
                ),
                PlanStep(
                    title="数据分析",
                    description="分析舆情数据并生成洞察",
                    estimated_time="20分钟"
                ),
                PlanStep(
                    title="报告生成",
                    description="生成分析报告",
                    estimated_time="10分钟",
                    skip_execute=True
                )
            ],
            estimated_time="60分钟"
        )





# ==================== Workflow Graph Construction ====================

def create_workflow_graph() -> StateGraph:
    """
    Create the brand event analysis workflow graph.

    Following open_deep_research pattern for graph construction.

    Returns:
        Compiled workflow graph
    """
    # Create the main workflow graph
    builder = StateGraph(
        WorkflowState,
        config_schema=WorkflowConfiguration
    )

    # Add all workflow nodes (removed summary node)
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("intent_clarification", intent_clarification_node)
    builder.add_node("planning", planning_node)
    builder.add_node("execution", execution_node)
    builder.add_node("report", report_node)

    # Define workflow edges
    # Start with supervisor for routing
    builder.add_edge(START, "supervisor")

    # Supervisor can route to any node or end
    builder.add_edge("supervisor", END)  # For greetings or completed workflows
    builder.add_edge("supervisor", "intent_clarification")
    builder.add_edge("supervisor", "planning")
    builder.add_edge("supervisor", "execution")
    builder.add_edge("supervisor", "report")

    # Intent clarification can end (for user feedback) or go to planning
    builder.add_edge("intent_clarification", END)
    builder.add_edge("intent_clarification", "planning")

    # Planning can end (for user feedback) or go to execution
    builder.add_edge("planning", END)
    builder.add_edge("planning", "execution")

    # Execution goes directly to report (removed summary)
    builder.add_edge("execution", "report")

    # Report ends the workflow
    builder.add_edge("report", END)

    return builder


# ==================== Workflow Instance ====================

# Create the compiled workflow graph
workflow_builder = create_workflow_graph()
workflow = workflow_builder.compile()


# ==================== Utility Functions ====================

async def run_workflow(
        initial_state: WorkflowState,
        config: Optional[Dict[str, Any]] = None,
        writer=None
) -> WorkflowState:
    """
    Run the brand event analysis workflow.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration
        writer: Optional writer function for streaming

    Returns:
        Final workflow state
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logger.info(f"Starting workflow execution, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Add writer to config if provided
        config["writer"] = writer

        # Create runnable config
        runnable_config = {"configurable": config}

        # Run the workflow
        final_state = await workflow.ainvoke(initial_state, config=runnable_config)

        logger.info(f"Workflow execution completed, session_id: {session_id}")
        return final_state

    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        # Return error state
        return {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_error",
                "message": str(e)
            }
        }


async def stream_workflow(
        initial_state: WorkflowState,
        config: Optional[Dict[str, Any]] = None
):
    """
    Stream the brand event analysis workflow execution.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration

    Yields:
        Workflow state updates
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logger.info(f"Starting workflow streaming, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Create runnable config
        runnable_config = {"configurable": config}

        # Stream the workflow
        async for chunk in workflow.astream(initial_state, config=runnable_config):
            yield chunk

        logger.info(f"Workflow streaming completed, session_id: {session_id}")

    except Exception as e:
        logger.error(f"Workflow streaming failed: {e}")
        # Yield error state
        yield {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_streaming_error",
                "message": str(e)
            }
        }


# ==================== Export All Functions ====================

__all__ = [
    "supervisor_node",
    "intent_clarification_node",
    "planning_node",
    "execution_node",
    "report_node",
    "workflow",
    "create_workflow_graph",
    "run_workflow",
    "stream_workflow"
]
