"""Main API for Brand Event Agent system."""

import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Literal
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage

from .workflow import BrandEventWorkflow, WorkflowState, WorkflowStatus
from .logger import get_workflow_logger, LogMessages

# ==================== API Schemas ====================

class ServiceToken(BaseModel):
    """Service token schema for IdaaS integration."""
    service_id: str = Field(..., description="服务ID（接入IdaaS）")
    access_token: str = Field(..., description="服务access_token")


class Extensions(BaseModel):
    """Extensions schema for additional features."""
    tokens: Optional[List[ServiceToken]] = Field(None, description="服务token列表")
    additional_fields: Dict[str, Any] = Field(default_factory=dict, description="其他扩展字段")


class ChatRequest(BaseModel):
    """Request schema for chat endpoint."""
    message: str = Field(..., description="用户发送的消息内容")
    session_id: Optional[str] = Field(None, description="会话ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="上传事件回调地址")
    extensions: Optional[Extensions] = Field(None, description="扩展字段")

    # 报表DSL字段
    report_dsl_data: Optional[Dict[str, Any]] = Field(None, description="报表DSL数据结构，用于生成报告")
    report_dsl_status: Optional[Literal["SUCCESS", "FAILED"]] = Field(None, description="报表DSL生成状态")
    report_dsl_message: Optional[str] = Field(None, description="报表DSL状态消息，FAILED时包含错误信息")

    # 工作流状态字段
    workflow_status: Optional[WorkflowStatus] = Field(None, description="期望的工作流状态，用于动态控制流程")

    # 保留原有字段以保持向后兼容
    user_id: Optional[str] = Field(None, description="用户ID（向后兼容）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据（向后兼容）")

class ChatResponse(BaseModel):
    """Response schema for chat endpoint."""
    session_id: str = Field(..., description="会话ID")
    response: str = Field(..., description="系统响应")
    status: str = Field(..., description="当前工作流状态")
    requires_feedback: bool = Field(False, description="是否需要用户反馈")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")

    # 新增字段
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="事件回调地址")
    extensions: Optional[Dict[str, Any]] = Field(None, description="扩展字段")


class SessionInfo(BaseModel):
    """Session information schema."""
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    status: str
    message_count: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SystemStatus(BaseModel):
    """System status schema."""
    status: str = Field(..., description="System status")
    version: str = Field(..., description="System version")
    uptime: str = Field(..., description="System uptime")
    active_sessions: int = Field(..., description="Number of active sessions")
    total_sessions: int = Field(..., description="Total sessions created")
    tools_available: int = Field(..., description="Number of available tools")
    agents_active: int = Field(..., description="Number of active agents")


class HealthCheck(BaseModel):
    """Health check response schema."""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    services: Dict[str, str] = Field(default_factory=dict)
    version: str = "1.0.0"
    tools_count: int = 0
    agents_count: int = 0

# ==================== Mock Event Handler ====================

class MockEventHandler:
    """Mock event handler for brand event agent."""

    def __init__(self):
        self.logger = get_workflow_logger("EventHandler")

    def send_live_status(self, text: str, state: Optional[Dict] = None):
        """Send live status message."""
        session_id = state.get("session_id") if state else "unknown"
        self.logger.info(f"Live Status: {text}", session_id=session_id)

    def send_agent_message(self, state: Optional[Dict], content: str, attachments=None):
        """Send agent message."""
        session_id = state.get("session_id") if state else "unknown"
        self.logger.info(f"Agent Message: {content[:50]}...", session_id=session_id)

    def send_agent_status(self, status: str, brief: str, description: str = None, state: Optional[Dict] = None):
        """Send agent status."""
        session_id = state.get("session_id") if state else "unknown"
        self.logger.info(f"Agent Status: {status} - {brief}", session_id=session_id)

# ==================== Main API Class ====================

class BrandEventAPI:
    """Main API class for Brand Event Agent system."""

    def __init__(self, checkpointer=None):
        self.logger = get_workflow_logger("API")
        self.start_time = datetime.now()

        # Initialize workflow
        self.workflow = BrandEventWorkflow(checkpointer=checkpointer)

        # Session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.total_sessions = 0

        # Mock event handler
        self.event_handler = MockEventHandler()

        self.logger.info("Brand Event API initialized successfully")

    def create_initial_state(
        self,
        session_id: str,
        user_input: str,
        user_id: str = "anonymous",
        task_id: Optional[str] = None,
        sandbox_id: Optional[str] = None,
        event_webhook: Optional[str] = None,
        extensions: Optional[Extensions] = None,
        report_dsl_data: Optional[Dict[str, Any]] = None,
        report_dsl_status: Optional[str] = None,
        report_dsl_message: Optional[str] = None,
        workflow_status: Optional[WorkflowStatus] = None
    ) -> WorkflowState:
        """Create initial workflow state."""

        return WorkflowState(
            session_id=session_id,
            messages=[HumanMessage(content=user_input)],
            workflow_status=workflow_status or WorkflowStatus.INITIALIZING,
            task_id=task_id,
            sandbox_id=sandbox_id,
            event_webhook=event_webhook,
            extensions=extensions.dict() if extensions else None,
            report_dsl_data=report_dsl_data,
            report_dsl_status=report_dsl_status,
            report_dsl_message=report_dsl_message,
            # 设置默认值
            intent_summary="",
            clarification_round=0,
            planning_round=0,
            intent_clarified=False,
            intent_approved=False,
            plan_approved=False,
        )
        """
        Create and configure FastAPI application.
        
        Returns:
            Configured FastAPI application
        """
        app = FastAPI(
            title="Brand Event Analysis API",
            description="Multi-agent system for brand sentiment analysis",
            version="1.0.0"
        )

    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Handle chat request."""
        try:
            # Generate or use existing session ID
            session_id = request.session_id or str(uuid.uuid4())

            self.logger.info(LogMessages.CHAT_REQUEST_START, session_id=session_id)

            # Check if this is a new session
            if session_id not in self.active_sessions:
                # Create new session
                self.active_sessions[session_id] = {
                    "created_at": datetime.now(),
                    "user_id": request.user_id,
                    "task_id": request.task_id,
                    "sandbox_id": request.sandbox_id,
                    "event_webhook": request.event_webhook,
                    "extensions": request.extensions.dict() if request.extensions else None,
                    "message_count": 1,
                    "workflow_status": request.workflow_status,
                }
                self.total_sessions += 1

                # Create initial state
                initial_state = self.create_initial_state(
                    session_id=session_id,
                    user_input=request.message,
                    user_id=request.user_id or "anonymous",
                    task_id=request.task_id,
                    sandbox_id=request.sandbox_id,
                    event_webhook=request.event_webhook,
                    extensions=request.extensions,
                    report_dsl_data=request.report_dsl_data,
                    report_dsl_status=request.report_dsl_status,
                    report_dsl_message=request.report_dsl_message,
                    workflow_status=request.workflow_status
                )

                # Run workflow with streaming
                config = {"configurable": {"thread_id": session_id}}
                final_state = await self._process_workflow_stream(initial_state, config, initial_state.dict())

            else:
                # Continue existing session
                self.active_sessions[session_id]["message_count"] += 1

                # Update session with new request data if provided
                if request.workflow_status:
                    self.active_sessions[session_id]["workflow_status"] = request.workflow_status

                # Get current state and update it with new data
                config = {"configurable": {"thread_id": session_id}}
                current_state = self.workflow.graph.get_state(config)
                state_for_events = current_state.values if current_state else {}

                # Add user message to continue conversation
                user_message = {"messages": [HumanMessage(content=request.message)]}

                # Update state with new report_dsl_data and workflow_status if provided
                if request.report_dsl_data or request.report_dsl_status or request.report_dsl_message or request.workflow_status:
                    update_data = {}
                    if request.report_dsl_data:
                        update_data["report_dsl_data"] = request.report_dsl_data
                    if request.report_dsl_status:
                        update_data["report_dsl_status"] = request.report_dsl_status
                    if request.report_dsl_message:
                        update_data["report_dsl_message"] = request.report_dsl_message
                    if request.workflow_status:
                        update_data["workflow_status"] = request.workflow_status

                    # Merge user message with state updates
                    user_message.update(update_data)

                # Continue workflow with streaming
                final_state = await self._process_workflow_stream(user_message, config, state_for_events)

            # Generate response from final state
            complete_state = final_state.values if final_state else {}
            response = self._generate_response(complete_state, session_id, request)

            self.logger.info(LogMessages.CHAT_REQUEST_COMPLETE, session_id=session_id)
            return response

        except Exception as e:
            self.logger.error(f"Error processing chat request: {e}", session_id=session_id, exc_info=True)

            error_response = ChatResponse(
                session_id=request.session_id or "error",
                response=f"抱歉，处理您的请求时发生了错误：{str(e)}",
                status="error",
                requires_feedback=False,
                task_id=request.task_id,
                sandbox_id=request.sandbox_id,
                event_webhook=request.event_webhook,
                extensions=request.extensions.dict() if request.extensions else None,
                metadata={"error": str(e)}
            )

            return error_response

    async def _process_workflow_stream(self, input_data, config, state_for_events):
        """Process workflow with streaming support."""
        try:
            # Create a mock writer that captures streaming data
            def writer(data: Dict[str, Any]):
                # Send streaming data to event handler
                if "live_status_message" in data:
                    self.event_handler.send_live_status(data["live_status_message"], state_for_events)
                elif "agent_message" in data:
                    self.event_handler.send_agent_message(state_for_events, data["agent_message"])
                elif "human_feedback_message" in data:
                    self.event_handler.send_agent_message(state_for_events, data["human_feedback_message"])
                elif "agent_message_with_file" in data:
                    content = data["agent_message_with_file"].get("content", "")
                    attachments = data["agent_message_with_file"].get("attachments", [])
                    self.event_handler.send_agent_message(state_for_events, content, attachments)

            # Run workflow with streaming
            async for chunk in self.workflow.graph.astream(input_data, config):
                # Process each chunk and call writer
                for node_name, node_output in chunk.items():
                    if node_output and hasattr(node_output, 'get'):
                        # Extract streaming messages from node output
                        if 'messages' in node_output:
                            messages = node_output['messages']
                            if messages and len(messages) > 0:
                                last_message = messages[-1]
                                if hasattr(last_message, 'content'):
                                    writer({"agent_message": last_message.content})

            # Get final state
            final_state = self.workflow.graph.get_state(config)
            return final_state

        except Exception as e:
            self.logger.error(f"Error in workflow streaming: {e}", exc_info=True)
            raise

    def _generate_response(self, state: Dict[str, Any], session_id: str, request: ChatRequest) -> ChatResponse:
        """Generate API response from workflow state."""
        try:
            # Determine response message and status
            workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)

            if workflow_status == WorkflowStatus.INITIALIZING:
                # Get the last AI message
                messages = state.get("messages", [])
                response_message = "您好！我是品牌舆情分析助手。"
                for msg in reversed(messages):
                    if hasattr(msg, 'content') and hasattr(msg, '__class__') and 'AI' in str(msg.__class__):
                        response_message = msg.content
                        break
                status = "waiting_feedback"
                requires_feedback = True

            elif workflow_status in [WorkflowStatus.CLARIFYING_INTENT, WorkflowStatus.PLANNING]:
                # Get the last AI message
                messages = state.get("messages", [])
                response_message = "请提供更多信息"
                for msg in reversed(messages):
                    if hasattr(msg, 'content') and hasattr(msg, '__class__') and 'AI' in str(msg.__class__):
                        response_message = msg.content
                        break
                status = "waiting_feedback"
                requires_feedback = True

            elif workflow_status in [WorkflowStatus.EXECUTING, WorkflowStatus.REPORT]:
                response_message = "分析正在进行中..."
                status = "processing"
                requires_feedback = False

            elif workflow_status == WorkflowStatus.FAILED:
                error_info = state.get("error_info", {})
                response_message = f"分析失败：{error_info.get('message', '未知错误')}"
                status = "failed"
                requires_feedback = False

            else:
                # Default case
                response_message = state.get("final_report", "分析已完成")
                status = "completed"
                requires_feedback = False

            return ChatResponse(
                session_id=session_id,
                response=response_message,
                status=status,
                requires_feedback=requires_feedback,
                task_id=request.task_id,
                sandbox_id=request.sandbox_id,
                event_webhook=request.event_webhook,
                extensions=request.extensions.dict() if request.extensions else None,
                metadata={
                    "workflow_status": str(workflow_status),
                    "intent_clarified": state.get("intent_clarified", False),
                    "plan_approved": state.get("plan_approved", False),
                    "final_report": state.get("final_report"),
                    "html_report": state.get("html_report")
                }
            )

        except Exception as e:
            self.logger.error(f"Error generating response: {e}", session_id=session_id, exc_info=True)
            return ChatResponse(
                session_id=session_id,
                response=f"生成响应时发生错误：{str(e)}",
                status="error",
                requires_feedback=False,
                metadata={"error": str(e)}
            )

    def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        if session_id not in self.active_sessions:
            return None

        session_data = self.active_sessions[session_id]
        return SessionInfo(
            session_id=session_id,
            user_id=session_data.get("user_id"),
            created_at=session_data.get("created_at"),
            updated_at=datetime.now(),
            status="active",
            message_count=session_data.get("message_count", 0),
            metadata=session_data
        )

    def get_system_status(self) -> SystemStatus:
        """Get system status information."""
        uptime = datetime.now() - self.start_time

        return SystemStatus(
            status="healthy",
            version="1.0.0",
            uptime=str(uptime),
            active_sessions=len(self.active_sessions),
            total_sessions=self.total_sessions,
            tools_available=0,  # TODO: Get actual tool count
            agents_active=1  # TODO: Get actual agent count
        )

    def health_check(self) -> HealthCheck:
        """Perform health check."""
        return HealthCheck(
            status="healthy",
            services={
                "workflow": "healthy",
                "event_handler": "healthy"
            },
            tools_count=0,  # TODO: Get actual tool count
            agents_count=1  # TODO: Get actual agent count
        )

