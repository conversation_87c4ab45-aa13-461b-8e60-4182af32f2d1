"""
Utility functions and tools for brand event analysis workflow.

This module combines utilities and tools from existing implementations
to maintain complete compatibility.
"""

import os
import tempfile
import httpx
import logging
import sys
import datetime
from typing import Dict, Any, Optional, List

import jwt
from langchain_core.messages import BaseMessage, HumanMessage


# ==================== Logging Configuration ====================

def get_logger(name: str) -> logging.Logger:
    """
    Get configured logger instance.
    
    Extracted from existing logging configuration to maintain compatibility.
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # Configure handler
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger

# ==================== Message Processing ====================

def get_latest_user_response(state: Dict[str, Any]) -> str:
    """
    Get the latest user response from state messages.
    
    Extracted from existing Agent implementations.
    
    Args:
        state: Current workflow state
        
    Returns:
        Latest user response
    """
    messages = state.get("messages", [])
    
    # Find the last human message
    for message in reversed(messages):
        if isinstance(message, HumanMessage):
            return message.content
    
    # Fallback to user_input if no human messages found
    return state.get("user_input", "")

def get_latest_plan_feedback(state: Dict[str, Any]) -> Optional[str]:
    """
    Get the latest plan feedback from user.
    
    Extracted from PlanningAgent implementation.
    
    Args:
        state: Current workflow state
        
    Returns:
        Latest plan feedback or None
    """
    messages = state.get("messages", [])
    
    # Look for human messages after task_plan was set
    if state.get("task_plan"):
        # Find messages after planning
        for message in reversed(messages):
            if isinstance(message, HumanMessage):
                content = message.content.strip()
                # Skip empty messages
                if content:
                    return content
    
    return None

def get_clarified_requirements(state: Dict[str, Any]) -> str:
    """
    Get clarified requirements from state.
    
    Extracted from existing Agent implementations.
    
    Args:
        state: Current workflow state
        
    Returns:
        Clarified requirements string
    """
    # First check if we have clarification result
    clarification_result = state.get("clarification_result")
    if clarification_result and isinstance(clarification_result, dict):
        # Extract structured requirements
        requirements_parts = []
        
        if clarification_result.get("brand_name"):
            requirements_parts.append(f"品牌：{clarification_result['brand_name']}")
        
        if clarification_result.get("analysis_scope"):
            requirements_parts.append(f"分析范围：{clarification_result['analysis_scope']}")
        
        if clarification_result.get("time_range"):
            requirements_parts.append(f"时间范围：{clarification_result['time_range']}")
        
        if clarification_result.get("analysis_type"):
            requirements_parts.append(f"分析类型：{clarification_result['analysis_type']}")
        
        if requirements_parts:
            return "；".join(requirements_parts)
    
    # Fallback to original user input
    return state.get("user_input", "")

def format_plan_message(execution_plan: Dict[str, Any], is_revision: bool = False) -> str:
    """
    Format execution plan for display.
    
    Extracted from PlanningAgent implementation.
    
    Args:
        execution_plan: Execution plan data
        is_revision: Whether this is a plan revision
        
    Returns:
        Formatted plan message
    """
    try:
        if is_revision:
            prefix = "已根据您的反馈调整了执行计划："
        else:
            prefix = "已为您制定了详细的执行计划："
        
        # Extract plan details
        title = execution_plan.get("title", "执行计划")
        objective = execution_plan.get("objective", "")
        steps = execution_plan.get("steps", [])
        
        # Build formatted message
        message_parts = [prefix, f"\n## {title}"]
        
        if objective:
            message_parts.append(f"\n**目标：** {objective}")
        
        if steps:
            message_parts.append("\n**执行步骤：**")
            for i, step in enumerate(steps, 1):
                step_title = step.get("title", f"步骤{i}")
                step_desc = step.get("description", "")
                message_parts.append(f"{i}. **{step_title}**")
                if step_desc:
                    message_parts.append(f"   {step_desc}")
        
        return "\n".join(message_parts)
        
    except Exception as e:
        logger = get_logger("BrandEventUtils")
        logger.error(f"Error formatting plan message: {e}")
        return "执行计划已生成，请查看详细内容。"

def build_message_history(messages: List[BaseMessage], max_messages: int = 5) -> str:
    """
    Build message history string for prompts.
    
    Args:
        messages: List of messages
        max_messages: Maximum number of messages to include
        
    Returns:
        Formatted message history
    """
    if not messages:
        return "无历史消息"
    
    # Get recent messages
    recent_messages = messages[-max_messages:] if len(messages) > max_messages else messages
    
    history_parts = []
    for i, message in enumerate(recent_messages):
        message_type = "用户" if isinstance(message, HumanMessage) else "助手"
        content = message.content[:200] + "..." if len(message.content) > 200 else message.content
        history_parts.append(f"{i+1}. {message_type}: {content}")
    
    return "\n".join(history_parts)

# ==================== Validation Functions ====================

def validate_input(input_text: str) -> bool:
    """
    Validate user input.
    
    Args:
        input_text: Input text to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not input_text or not isinstance(input_text, str):
        return False
    
    # Check minimum length
    if len(input_text.strip()) < 2:
        return False
    
    # Check for obvious spam or invalid content
    spam_indicators = ["test", "测试", "111", "aaa"]
    text_lower = input_text.lower().strip()
    
    if text_lower in spam_indicators:
        return False
    
    return True

def validate_execution_plan(execution_plan: Dict[str, Any]) -> bool:
    """
    Validate execution plan structure.
    
    Args:
        execution_plan: Execution plan to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not execution_plan or not isinstance(execution_plan, dict):
        return False
    
    # Check required fields
    required_fields = ["title", "steps"]
    for field in required_fields:
        if field not in execution_plan:
            return False
    
    # Check steps structure
    steps = execution_plan.get("steps", [])
    if not isinstance(steps, list) or len(steps) == 0:
        return False
    
    # Validate each step
    for step in steps:
        if not isinstance(step, dict):
            return False
        if "title" not in step:
            return False
    
    return True

# ==================== Stream Writer Type ====================

class StreamWriter:
    """
    Stream writer interface for compatibility.

    This maintains compatibility with existing Agent implementations.
    """

    def __init__(self, writer_func):
        self.writer_func = writer_func

    def __call__(self, data: Dict[str, Any]):
        """Write data to stream."""
        if self.writer_func:
            self.writer_func(data)

# ==================== Authentication Tools ====================
# 配置信息（需与验证函数保持一致）
SECRET_KEY = "brand_event"  # 必须与验证函数相同
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60  # Token有效期（1小时）
def create_access_token(user_id: str) -> str:
    """
    生成JWT Token（与verify_token_and_user配套）

    参数:
        user_id: 用户唯一标识，将作为'sub'存入Token

    返回:
        str: 签名的JWT Token
    """
    # 设置过期时间
    expire = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        minutes=ACCESS_TOKEN_EXPIRE_MINUTES
    )

    # 构建Payload（必须包含sub字段以匹配验证逻辑）
    to_encode = {
        "sub": user_id,          # 用户ID（subject标准字段）
        "exp": expire,            # 过期时间
        "iat": datetime.datetime.now(datetime.timezone.utc),  # 签发时间
        "type": "access"          # 可选的Token类型标识
    }

    # 生成Token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
# ==================== Report Generation Tools ====================

async def generate_report(
    report_dsl: Dict[str, Any],
    base_url: str = "https://console-playground.fed.chehejia.com",
    timeout: int = 300
) -> Dict[str, Any]:
    """
    Call external report service to generate report.

    Extracted from ReportAgent to maintain compatibility.

    Args:
        report_dsl: Report DSL data structure
        base_url: Report service base URL
        timeout: Request timeout in seconds

    Returns:
        Report generation result
    """
    try:
        logger = get_logger("BrandEventTools")
        logger.info("Starting report generation")

        # Build request URL
        url = f"{base_url}/__ui/report"

        # Build request headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer api-key:eyJhbGciOiJIUzI1NiJ9.eyJzIjoidWkiLCJpYXQiOjE3NDk2MzI5NDgsImlzcyI6IjdONVg5MHNkOUtuZGlDS1Q1VjlKWGwifQ.yKQahvhNPXjGCfmFHEJVl9iMWqGodm_QJ8GHjKxUuHI",
        }

        # Send request
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url=url,
                headers=headers,
                json=report_dsl
            )

            # Check response status
            if response.status_code == 200:
                # Interface returns HTML format report
                html_content = response.text
                logger.info("Report generated successfully")
                return {
                    "success": True,
                    "html_content": html_content,
                    "content_type": "text/html",
                    "message": "报告生成成功"
                }
            else:
                logger.error(f"Report generation failed: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "message": "报告生成失败"
                }

    except httpx.TimeoutException:
        logger = get_logger("BrandEventTools")
        logger.error("Report generation timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "报告生成超时"
        }
    except Exception as e:
        logger = get_logger("BrandEventTools")
        logger.error(f"Report generation error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"报告生成失败：{str(e)}"
        }

async def upload_html_to_s3(
    html_content: str,
    filename: Optional[str] = None,
    upload_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload",
    timeout: int = 300
) -> Dict[str, Any]:
    """
    Upload HTML content to S3 storage.

    Extracted from ReportAgent to maintain compatibility.

    Args:
        html_content: HTML content to upload
        filename: File name (optional, auto-generated if not provided)
        upload_url: Upload service URL
        timeout: Request timeout in seconds

    Returns:
        Upload result
    """
    try:
        logger = get_logger("BrandEventTools")
        logger.info("Starting HTML upload to S3")

        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
            filename = f"report-{timestamp}.html"

        # Create temporary file
        temp_file_path = os.path.join(tempfile.gettempdir(), filename)

        # Write HTML content to temporary file
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        try:
            # Prepare upload file
            with open(temp_file_path, 'rb') as file:
                files = {
                    'file': (filename, file, 'text/html')
                }

                # Send upload request
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        url=upload_url,
                        files=files
                    )

                    # Check response status
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"HTML uploaded successfully: {result}")
                        return {
                            "success": True,
                            "upload_result": result,
                            "filename": filename,
                            "message": "HTML报告上传成功"
                        }
                    else:
                        logger.error(f"HTML upload failed: {response.status_code} - {response.text}")
                        return {
                            "success": False,
                            "error": f"HTTP {response.status_code}: {response.text}",
                            "message": "HTML报告上传失败"
                        }

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger = get_logger("BrandEventTools")
        logger.error(f"HTML upload error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告上传失败：{str(e)}"
        }

# ==================== DSL Processing Tools ====================

def parse_dsl_summary(report_dsl: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse AI summary and key data from DSL.

    Extracted from ReportAgent to maintain compatibility.

    Args:
        report_dsl: Report DSL data structure

    Returns:
        Parsed summary data
    """
    try:
        logger = get_logger("BrandEventTools")
        logger.info("Parsing DSL summary data")

        # Extract AI summary from DSL structure
        ai_summary = ""
        key_metrics = {}

        # Parse DSL sections for summary content
        sections = report_dsl.get("sections", {})

        for section_id, section_data in sections.items():
            if isinstance(section_data, dict):
                section_type = section_data.get("type", "")
                section_title = section_data.get("title", "")

                # Extract summary from summary sections
                if "summary" in section_type.lower() or "总结" in section_title:
                    content = section_data.get("content", [])
                    if content and isinstance(content, list):
                        for item in content:
                            if isinstance(item, dict) and item.get("type") == "text":
                                ai_summary += item.get("data", "") + "\n"

                # Extract metrics from metrics sections
                elif "metrics" in section_type.lower() or "指标" in section_title:
                    content = section_data.get("content", [])
                    if content and isinstance(content, list):
                        for item in content:
                            if isinstance(item, dict) and item.get("type") == "metrics":
                                metrics_data = item.get("data", [])
                                for metric in metrics_data:
                                    if isinstance(metric, dict):
                                        label = metric.get("label", "")
                                        value = metric.get("value", "")
                                        if label and value:
                                            key_metrics[label] = value

        # Default summary if none found
        if not ai_summary.strip():
            ai_summary = "品牌舆情分析报告已生成，包含详细的数据分析和洞察。"

        result = {
            "ai_summary": ai_summary.strip(),
            "key_metrics": key_metrics,
            "sections_count": len(sections),
            "report_type": "brand_sentiment_analysis"
        }

        logger.info("DSL summary parsing completed")
        return result

    except Exception as e:
        logger = get_logger("BrandEventTools")
        logger.error(f"Error parsing DSL summary: {e}")
        return {
            "ai_summary": "报告解析过程中出现错误，请查看完整报告获取详细信息。",
            "key_metrics": {},
            "sections_count": 0,
            "report_type": "brand_sentiment_analysis"
        }

def create_brand_analysis_dsl() -> Dict[str, Any]:
    """
    Create default brand analysis DSL structure.

    Extracted from ReportAgent to maintain compatibility.

    Returns:
        Default brand analysis DSL
    """
    return {
        "title": "品牌舆情分析报告",
        "description": "基于多维度数据的品牌舆情深度分析",
        "sections": {
            "01": {
                "type": "summary",
                "title": "执行摘要",
                "description": "品牌舆情分析核心发现",
                "content": [
                    {
                        "type": "text",
                        "data": "本报告基于全网数据采集和AI智能分析，为品牌提供全面的舆情洞察。通过多维度数据分析，识别品牌传播趋势、用户情感倾向和潜在风险机会。"
                    }
                ]
            },
            "02": {
                "type": "analysis",
                "title": "详细分析",
                "description": "品牌舆情多维度深度分析",
                "content": [
                    {
                        "type": "chart",
                        "chart_type": "line",
                        "title": "舆情趋势分析",
                        "data": {
                            "labels": ["第1周", "第2周", "第3周", "第4周"],
                            "datasets": [
                                {
                                    "label": "正面舆情",
                                    "data": [65, 70, 75, 80],
                                    "borderColor": "#10B981"
                                },
                                {
                                    "label": "负面舆情",
                                    "data": [20, 15, 12, 10],
                                    "borderColor": "#EF4444"
                                }
                            ]
                        }
                    }
                ]
            },
            "03": {
                "type": "section",
                "title": "关键指标",
                "description": "品牌舆情关键性能指标",
                "content": [
                    {
                        "type": "metrics",
                        "data": [
                            {"label": "舆情健康度", "value": "85%", "trend": "up"},
                            {"label": "传播声量", "value": "12,450", "trend": "up"},
                            {"label": "正面占比", "value": "78.5%", "trend": "stable"},
                            {"label": "负面占比", "value": "12.3%", "trend": "down"}
                        ]
                    }
                ]
            }
        }
    }
