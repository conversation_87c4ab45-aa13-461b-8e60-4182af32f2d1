"""FastAPI application for Brand Event Agent."""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import async<PERSON>
import j<PERSON>
from typing import Async<PERSON>enerator

from .api import BrandEventAPI, ChatRequest, ChatResponse, SessionInfo, SystemStatus, HealthCheck


def create_app() -> FastAPI:
    """Create FastAPI application."""
    
    # Initialize API
    api = BrandEventAPI()
    
    # Create FastAPI app
    app = FastAPI(
        title="Brand Event Agent API",
        description="API for brand event analysis and workflow management",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.post("/chat", response_model=ChatResponse)
    async def chat(request: ChatRequest) -> ChatResponse:
        """
        Main chat endpoint - maintains compatibility with existing API.
        
        Args:
            request: Chat request with user message and context
            
        Returns:
            Chat response with system reply and status
        """
        try:
            response = await api.chat(request)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/chat/stream")
    async def chat_stream(request: ChatRequest):
        """
        Streaming chat endpoint for real-time updates.
        
        Args:
            request: Chat request with user message and context
            
        Returns:
            Streaming response with real-time updates
        """
        try:
            return StreamingResponse(
                _stream_chat_response(api, request),
                media_type="text/plain"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/session/{session_id}", response_model=SessionInfo)
    async def get_session(session_id: str) -> SessionInfo:
        """
        Get session information.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session information
        """
        session_info = api.get_session_info(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")
        return session_info
    
    @app.delete("/session/{session_id}")
    async def delete_session(session_id: str):
        """
        Delete a session and its data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Success message
        """
        if session_id in api.active_sessions:
            del api.active_sessions[session_id]
            return {"message": "Session deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Session not found")
    
    @app.get("/status", response_model=SystemStatus)
    async def get_system_status() -> SystemStatus:
        """Get system status information."""
        return api.get_system_status()
    
    @app.get("/health", response_model=HealthCheck)
    async def health_check() -> HealthCheck:
        """Health check endpoint."""
        return api.health_check()
    
    return app


async def _stream_chat_response(api: BrandEventAPI, request: ChatRequest) -> AsyncGenerator[str, None]:
    """
    Stream chat response with real-time updates.
    
    Args:
        api: Brand event API instance
        request: Chat request
        
    Yields:
        Streaming response data in SSE format
    """
    try:
        # Generate session ID if not provided
        session_id = request.session_id or "stream_session"
        
        # Send initial status
        yield f"data: {json.dumps({'type': 'status', 'message': '开始处理请求...', 'session_id': session_id})}\n\n"
        
        # Process chat request
        response = await api.chat(request)
        
        # Send response data
        yield f"data: {json.dumps({'type': 'response', 'data': response.dict()})}\n\n"
        
        # Send completion signal
        yield f"data: {json.dumps({'type': 'complete', 'session_id': session_id})}\n\n"
        
    except Exception as e:
        # Send error
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
