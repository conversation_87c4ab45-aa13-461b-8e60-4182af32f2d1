"""FastAPI server for Specific V4 system."""

import logging
from typing import List, Optional

from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.api import SpecificAPI
from src.api.schemas import (
    ChatRequest, ChatResponse, SessionInfo, SystemStatus,
    WorkflowStatus, HealthCheck, ToolInfo, AgentInfo, ErrorResponse
)

load_dotenv()

# Global API instance
api_instance: Optional[SpecificAPI] = None


def get_api() -> SpecificAPI:
    """Get API instance."""
    global api_instance
    if api_instance is None:
        api_instance = SpecificAPI()
    return api_instance


def create_app() -> FastAPI:
    """Create FastAPI application."""
    
    app = FastAPI(
        title="Specific Multi-Agent System",
        description="A simplified multi-agent system built with LangGraph using supervisor pattern",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """Global exception handler."""
        logging.error(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error_type="internal_error",
                message="Internal server error",
                details=str(exc),
                recoverable=False
            ).dict()
        )
    
    @app.post("/chat", response_model=ChatResponse)
    async def chat_endpoint(request: ChatRequest, api: SpecificAPI = Depends(get_api)):
        """Chat endpoint for multi-turn conversations."""
        try:
            return await api.chat(request)
        except Exception as e:
            logging.error(f"Chat endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/session/{session_id}", response_model=SessionInfo)
    async def get_session_endpoint(session_id: str, api: SpecificAPI = Depends(get_api)):
        """Get session information."""
        session_info = await api.get_session_info(session_id)
        if session_info is None:
            raise HTTPException(status_code=404, detail="Session not found")
        return session_info

    @app.get("/session/{session_id}/status", response_model=WorkflowStatus)
    async def get_workflow_status_endpoint(session_id: str, api: SpecificAPI = Depends(get_api)):
        """Get workflow status for a session."""
        status = await api.get_workflow_status(session_id)
        if status is None:
            raise HTTPException(status_code=404, detail="Session not found or no workflow status available")
        return status

    @app.get("/system/status", response_model=SystemStatus)
    async def get_system_status_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get system status."""
        return await api.get_system_status()

    @app.get("/system/tools", response_model=List[ToolInfo])
    async def get_tools_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get available tools."""
        return await api.get_available_tools()

    @app.get("/system/agents", response_model=List[AgentInfo])
    async def get_agents_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get agent information."""
        return await api.get_agents_info()

    @app.get("/health", response_model=HealthCheck)
    async def health_check_endpoint(api: SpecificAPI = Depends(get_api)):
        """Health check endpoint."""
        return await api.health_check()

    @app.post("/system/cleanup")
    async def cleanup_sessions_endpoint(
        max_age_hours: int = 24,
        api: SpecificAPI = Depends(get_api)
    ):
        """Clean up old sessions."""
        cleaned_count = await api.cleanup_old_sessions(max_age_hours)
        return {"message": f"Cleaned up {cleaned_count} old sessions"}

    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Specific V4 Multi-Agent System",
            "version": "0.4.0",
            "docs": "/docs",
            "health": "/health"
        }
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
