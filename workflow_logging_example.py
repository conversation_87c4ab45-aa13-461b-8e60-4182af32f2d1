"""
展示如何在workflow.py中使用简化日志系统的示例
"""

from src.brand_event_agent.logger import (
    get_workflow_logger, LogMessages, log_workflow_operation
)
from src.brand_event_agent.messages import agent_msg, status_msg, feedback_msg

# ==================== 使用示例 ====================

def example_supervisor_node_with_logging(state, writer, config):
    """
    使用简化日志的supervisor节点示例
    """
    # 创建日志器
    logger = get_workflow_logger("SupervisorNode")
    session_id = state.get('session_id', 'unknown')
    
    # 原来的硬编码日志方式：
    # logger.info(f"Start doing workflow supervision, session_id:{session_id}")
    
    # 使用标准化日志消息：
    logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)
    
    try:
        # 发送用户界面消息（使用i18n）
        writer(status_msg("supervisor.analyzing_state"))
        writer(agent_msg("supervisor.greeting_help"))
        
        # 模拟路由决策
        destination = "intent_clarification"
        reason = "需要澄清用户意图"
        
        # 原来的硬编码日志方式：
        # logger.info(f"Completed workflow supervision, result: route to {destination}, reason: {reason}, session_id:{session_id}")
        
        # 使用标准化日志消息：
        logger.info(LogMessages.WORKFLOW_SUPERVISION_COMPLETE.format(
            destination=destination, reason=reason), session_id=session_id)
        
        return {"goto": destination}
        
    except Exception as e:
        # 原来的硬编码日志方式：
        # logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")
        
        # 使用标准化日志消息：
        logger.error(LogMessages.WORKFLOW_SUPERVISION_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)
        
        writer(status_msg("supervisor.routing_error"))
        return {"goto": "intent_clarification"}


def example_execution_node_with_logging(state, writer, config):
    """
    使用简化日志的执行节点示例
    """
    # 创建日志器
    logger = get_workflow_logger("ExecutionNode")
    session_id = state.get('session_id', 'unknown')
    task_plan_dict = state.get("task_plan", {})
    
    try:
        if not task_plan_dict:
            # 使用标准化错误日志消息
            logger.error(LogMessages.ERROR_NO_EXECUTION_PLAN, session_id=session_id)
            return {"goto": "__end__", "workflow_status": "FAILED"}
        
        execution_plan = task_plan_dict  # 简化处理
        total_steps = len(execution_plan.get("steps", []))
        
        # 使用标准化日志消息
        logger.info(LogMessages.WORKFLOW_EXECUTION_START.format(total_steps=total_steps), 
                   session_id=session_id)
        
        # 发送用户界面消息（使用i18n）
        writer(agent_msg("execution.starting"))
        
        # 执行步骤
        for step_index, plan_step in enumerate(execution_plan.get("steps", [])):
            step_title = plan_step.get("title", f"步骤{step_index + 1}")
            
            if plan_step.get("skip_execute", False):
                # 使用标准化日志消息
                logger.info(LogMessages.STEP_SKIPPED.format(
                    step_index=step_index + 1, step_title=step_title), session_id=session_id)
                continue
            
            # 发送用户界面消息（使用i18n）
            writer(status_msg("execution.executing_step", step_name=step_title))
            writer(agent_msg("execution.step_start", 
                           step_index=step_index + 1, 
                           step_title=step_title))
            
            try:
                # 使用标准化日志消息
                logger.info(LogMessages.STEP_START.format(
                    step_index=step_index, step_title=step_title), session_id=session_id)
                
                # 模拟步骤执行
                step_result = f"步骤{step_index + 1}执行完成"
                
                # 使用标准化日志消息
                logger.info(LogMessages.STEP_COMPLETE.format(step_title=step_title), 
                           session_id=session_id)
                
                # 发送用户界面消息（使用i18n）
                writer(agent_msg("execution.step_completed", 
                               step_index=step_index + 1, 
                               result=step_result))
                
            except Exception as e:
                # 使用标准化错误日志消息
                logger.error(LogMessages.STEP_FAILED.format(
                    step_title=step_title, error=str(e)), session_id=session_id)
        
        # 完成执行
        writer(agent_msg("execution.all_completed"))
        logger.info(LogMessages.WORKFLOW_EXECUTION_COMPLETE, session_id=session_id)
        
        return {"goto": "report", "workflow_status": "REPORT"}
        
    except Exception as e:
        # 使用标准化错误日志消息
        logger.error(LogMessages.WORKFLOW_EXECUTION_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)
        
        return {"goto": "__end__", "workflow_status": "FAILED"}


def example_report_node_with_logging(state, writer, config):
    """
    使用简化日志的报告节点示例
    """
    # 创建日志器
    logger = get_workflow_logger("ReportNode")
    session_id = state.get('session_id', 'unknown')
    
    try:
        # 使用标准化日志消息
        logger.info(LogMessages.WORKFLOW_REPORT_START, session_id=session_id)
        
        # 发送用户界面消息（使用i18n）
        writer(agent_msg("report.generating"))
        
        # 模拟系统操作日志
        logger.info(LogMessages.REPORT_SERVICE_CALL, session_id=session_id)
        writer(status_msg("report.calling_service"))
        
        # 模拟S3上传
        logger.info(LogMessages.S3_UPLOAD_START, session_id=session_id)
        writer(status_msg("report.uploading"))
        
        # 模拟成功上传
        upload_result = {"fileKey": "report-123.html", "url": "https://example.com/report.html"}
        logger.info(LogMessages.S3_UPLOAD_SUCCESS.format(result=str(upload_result)), 
                   session_id=session_id)
        
        # 模拟DSL解析
        ai_summary_length = 150
        metrics_count = 5
        viewpoints_count = 3
        logger.info(LogMessages.DSL_PARSING.format(
            ai_summary_length=ai_summary_length,
            metrics_count=metrics_count,
            viewpoints_count=viewpoints_count), session_id=session_id)
        
        writer(status_msg("report.parsing_summary"))
        
        # 完成报告生成
        writer(agent_msg("report.completed"))
        writer(feedback_msg("report.other_help"))
        
        # 使用标准化日志消息
        logger.info(LogMessages.WORKFLOW_REPORT_COMPLETE, session_id=session_id)
        
        return {"goto": "__end__", "workflow_status": "COMPLETED"}
        
    except Exception as e:
        # 使用标准化错误日志消息
        logger.error(LogMessages.WORKFLOW_REPORT_FAILED.format(error=str(e)), 
                    session_id=session_id, exc_info=True)
        
        writer(agent_msg("report.generation_failed", error=str(e)))
        return {"goto": "__end__", "workflow_status": "FAILED"}


# ==================== 使用装饰器的示例 ====================

@log_workflow_operation("workflow supervision")
def example_supervisor_with_decorator(state, writer, config):
    """
    使用装饰器自动记录日志的supervisor节点示例
    """
    session_id = state.get('session_id', 'unknown')
    
    # 装饰器会自动记录开始和完成日志
    # 只需要记录具体的业务逻辑日志
    logger = get_workflow_logger("SupervisorNode")
    
    # 用户消息分析
    message_type = "task_request"
    logger.info(LogMessages.USER_MESSAGE_ANALYSIS.format(message_type=message_type), 
               session_id=session_id)
    
    # LLM路由决策
    next_node = "intent_clarification"
    reason = "User intent needs clarification"
    logger.info(LogMessages.LLM_ROUTING_DECISION.format(next=next_node, reason=reason), 
               session_id=session_id)
    
    # 发送用户界面消息
    writer(status_msg("supervisor.analyzing_state"))
    writer(agent_msg("supervisor.greeting_help"))
    
    return {"goto": next_node}


@log_workflow_operation("task execution")
async def example_execution_with_decorator(state, writer, config):
    """
    使用装饰器自动记录日志的异步执行节点示例
    """
    session_id = state.get('session_id', 'unknown')
    logger = get_workflow_logger("ExecutionNode")
    
    # JWT Token生成
    user_id = "user_123"
    logger.info(LogMessages.JWT_TOKEN_GENERATED.format(user_id=user_id), 
               session_id=session_id)
    
    # MCP工具加载
    tool_count = 5
    logger.info(LogMessages.MCP_TOOLS_LOADED.format(tool_count=tool_count), 
               session_id=session_id)
    
    # 发送用户界面消息
    writer(agent_msg("execution.starting"))
    
    # 模拟异步执行
    import asyncio
    await asyncio.sleep(0.1)  # 模拟异步操作
    
    writer(agent_msg("execution.all_completed"))
    
    return {"goto": "report"}


# ==================== 实际应用建议 ====================

"""
在实际的workflow.py中应用简化日志系统的步骤：

1. 导入日志工具：
   from .logger import get_workflow_logger, LogMessages, log_workflow_operation

2. 在每个节点函数开始创建日志器：
   logger = get_workflow_logger("SupervisorNode")  # 或其他节点名称
   session_id = state.get('session_id', 'unknown')

3. 替换硬编码日志为标准化消息：
   # 原来：
   logger.info(f"Start doing workflow supervision, session_id:{session_id}")
   
   # 改为：
   logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)

4. 处理带参数的日志：
   # 原来：
   logger.info(f"Completed step: {step_title}, session_id:{session_id}")
   
   # 改为：
   logger.info(LogMessages.STEP_COMPLETE.format(step_title=step_title), session_id=session_id)

5. 错误日志处理：
   # 原来：
   logger.error(f"Execution failed: {e}, session_id:{session_id}")
   
   # 改为：
   logger.error(LogMessages.WORKFLOW_EXECUTION_FAILED.format(error=str(e)), 
                session_id=session_id, exc_info=True)

6. 可选：使用装饰器自动记录节点开始/完成：
   @log_workflow_operation("workflow supervision")
   def supervisor_node(state, writer, config):
       # 节点逻辑

优势：
- 日志格式统一，与现有logger.py保持一致
- 标准化的日志消息，便于监控和分析
- 支持参数化日志消息
- 保持与现有日志系统的兼容性
- 支持会话ID和上下文信息
- 不需要i18n，专注于开发和运维需求
- 可选的装饰器支持，减少重复代码
"""

if __name__ == "__main__":
    # 设置基本日志配置
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 模拟测试
    print("=== 测试日志输出 ===")
    
    # 模拟状态和writer
    test_state = {"session_id": "test_session_123"}
    test_writer = lambda msg: print(f"Writer: {msg}")
    test_config = {}
    
    # 测试不同节点的日志
    example_supervisor_node_with_logging(test_state, test_writer, test_config)
    print()
    example_supervisor_with_decorator(test_state, test_writer, test_config)
