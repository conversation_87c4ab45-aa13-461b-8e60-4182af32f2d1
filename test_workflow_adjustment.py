#!/usr/bin/env python3
"""
测试调整后的workflow.py - 验证去掉summary节点后的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_workflow_file_syntax():
    """测试workflow.py文件语法"""
    print("🧪 测试workflow.py文件语法...")

    try:
        # 尝试导入workflow模块
        from src.brand_event_agent import workflow
        print("✅ Workflow模块导入成功")

        # 检查关键函数是否存在
        required_functions = [
            'create_workflow_graph',
            'execution_node',
            'report_node',
            'supervisor_node',
            'intent_clarification_node',
            'planning_node'
        ]

        for func_name in required_functions:
            assert hasattr(workflow, func_name), f"缺少函数: {func_name}"
            print(f"✅ 函数 {func_name} 存在")

        # 检查summary_node是否已被移除
        assert not hasattr(workflow, 'summary_node'), "summary_node应该已被移除"
        print("✅ summary_node已成功移除")

        return True

    except Exception as e:
        print(f"❌ Workflow文件语法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_workflow_graph_structure():
    """测试workflow图结构"""
    print("\n🧪 测试workflow图结构...")

    try:
        from src.brand_event_agent.workflow import create_workflow_graph

        # 创建workflow图
        builder = create_workflow_graph()
        print("✅ Workflow图构建器创建成功")

        # 检查构建器类型
        from langgraph.graph import StateGraph
        assert isinstance(builder, StateGraph), "构建器应该是StateGraph类型"
        print("✅ 构建器类型正确")

        return True

    except Exception as e:
        print(f"❌ Workflow图结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_node_functions():
    """测试节点函数"""
    print("\n🧪 测试节点函数...")

    try:
        from src.brand_event_agent.workflow import (
            execution_node,
            report_node,
            supervisor_node,
            intent_clarification_node,
            planning_node
        )

        # 检查函数签名
        import inspect

        nodes_to_test = [
            ("execution_node", execution_node),
            ("report_node", report_node),
            ("supervisor_node", supervisor_node),
            ("intent_clarification_node", intent_clarification_node),
            ("planning_node", planning_node)
        ]

        for node_name, node_func in nodes_to_test:
            sig = inspect.signature(node_func)
            params = list(sig.parameters.keys())

            expected_params = ["state", "writer", "config"]
            for param in expected_params:
                assert param in params, f"{node_name}缺少参数: {param}"

            print(f"✅ {node_name} 函数签名正确")

        return True

    except Exception as e:
        print(f"❌ 节点函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_execution_logic_structure():
    """测试execution逻辑结构"""
    print("\n🧪 测试execution逻辑结构...")

    try:
        # 检查JWT相关常量
        from src.brand_event_agent.workflow import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
        assert SECRET_KEY == "brand_event", "SECRET_KEY应该是'brand_event'"
        assert ALGORITHM == "HS256", "ALGORITHM应该是'HS256'"
        assert ACCESS_TOKEN_EXPIRE_MINUTES == 2880, "ACCESS_TOKEN_EXPIRE_MINUTES应该是2880"

        print("✅ JWT配置正确")
        print("✅ Execution逻辑已内联到execution_node中")

        return True

    except Exception as e:
        print(f"❌ Execution逻辑结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_report_logic_structure():
    """测试report逻辑结构"""
    print("\n🧪 测试report逻辑结构...")

    try:
        # 验证辅助函数已被移除
        from src.brand_event_agent import workflow

        # 这些函数应该不存在了
        helper_functions = [
            '_parse_dsl_summary',
            '_generate_text_summary',
            '_generate_report_html',
            '_upload_html_to_s3_service',
            '_create_brand_analysis_dsl_default'
        ]

        for func_name in helper_functions:
            assert not hasattr(workflow, func_name), f"辅助函数 {func_name} 应该已被移除"

        print("✅ Report辅助函数已成功移除")
        print("✅ Report逻辑已内联到report_node中")

        return True

    except Exception as e:
        print(f"❌ Report逻辑结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_helper_functions_removed():
    """测试辅助函数是否已被移除"""
    print("\n🧪 测试辅助函数是否已被移除...")

    try:
        from src.brand_event_agent import workflow

        # 这些辅助函数应该都不存在了
        removed_functions = [
            '_llm_based_routing',
            '_analyze_user_intent',
            '_is_intent_clear',
            '_analyze_plan_feedback_intent',
            '_create_execution_plan',
            '_get_execution_agent',
            '_execute_single_step',
            '_execute_step_content',
            '_create_access_token',
            '_parse_dsl_summary',
            '_generate_text_summary',
            '_generate_report_html',
            '_upload_html_to_s3_service',
            '_create_brand_analysis_dsl_default'
        ]

        for func_name in removed_functions:
            assert not hasattr(workflow, func_name), f"辅助函数 {func_name} 应该已被移除"

        print("✅ 所有辅助函数已成功移除")
        print("✅ 所有逻辑已内联到对应的节点函数中")

        return True

    except Exception as e:
        print(f"❌ 辅助函数移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试调整后的workflow.py")
    print("=" * 50)

    # 运行所有测试
    tests = [
        test_workflow_file_syntax,
        test_workflow_graph_structure,
        test_node_functions,
        test_execution_logic_structure,
        test_report_logic_structure,
        test_helper_functions_removed,
    ]

    # 同步测试
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)

    # 汇总结果
    passed = sum(results)
    total = len(results)

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！workflow调整成功")
        print("\n✅ 主要调整内容:")
        print("  - 去掉了summary节点")
        print("  - execution节点直接跳转到report节点")
        print("  - execution_node核心逻辑与ExecutionAgent.execute一致")
        print("  - report_node核心逻辑与ReportAgent.execute一致")
        print("  - 保持了JWT配置和MCP工具集成")
        print("  - 所有辅助函数逻辑已内联到对应节点函数中")
        print("  - 代码结构更加简洁，所有逻辑都在节点内部")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
