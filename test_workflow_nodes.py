#!/usr/bin/env python3
"""
Workflow节点真实测试用例
测试各个节点的不同分支条件和逻辑 - 真实调用，只mock writer
"""

import asyncio
import logging
from typing import Dict, Any, List
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入需要的模块
from src.brand_event_agent.workflow import (
    supervisor_node, intent_clarification_node, planning_node,
    execution_node, report_node, WorkflowState, WorkflowStatus
)
from src.brand_event_agent.config import WorkflowConfiguration
from langchain_core.messages import HumanMessage, AIMessage


class MockWriter:
    """模拟writer，收集所有输出消息"""
    
    def __init__(self):
        self.messages = []
    
    def __call__(self, message):
        self.messages.append(message)
        print(f"Writer: {message}")
    
    def get_messages(self):
        return self.messages
    
    def clear(self):
        self.messages = []


def create_base_config():
    """创建基础配置 - 使用真实的配置"""
    return {
        "configurable": {
            "supervisor_model": "azure-gpt-4o-mini",
            "analyst_model": "azure-gpt-4o-mini",
            "planner_model": "azure-gpt-4o-mini",
            "executor_model": "azure-gpt-4o-mini",
            "summarizer_model": "azure-gpt-4o-mini",
            "reporter_model": "azure-gpt-4o-mini",
            "openai_api_key": "test",
            "openai_base_url": "https://llm-model-proxy.dev.fc.chj.cloud/agentops",
            "report_base_url": "https://console-playground.fed.chehejia.com",
            "upload_url": "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload",
            "report_timeout": 30
        }
    }


def create_test_state(
    session_id: str = "test_session_123",
    messages: List = None,
    workflow_status: WorkflowStatus = WorkflowStatus.INITIALIZING,
    **kwargs
) -> WorkflowState:
    """创建测试用的WorkflowState"""
    
    if messages is None:
        messages = [HumanMessage(content="我想分析京东品牌的舆情情况")]
    
    state = WorkflowState(
        session_id=session_id,
        messages=messages,
        workflow_status=workflow_status,
        **kwargs
    )
    
    return state


# ==================== Supervisor Node 测试用例 ====================

async def test_supervisor_node_greeting():
    """测试supervisor_node - 问候消息分支 (真实调用)"""
    print("\n🧪 测试 supervisor_node - 问候消息分支")

    # 准备测试数据 - 问候消息
    state = create_test_state(
        session_id="test_greeting_001",
        messages=[HumanMessage(content="你好")],
        workflow_status=WorkflowStatus.INITIALIZING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用supervisor_node
        result = await supervisor_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 路由原因: {result.update.get('reason', 'N/A')}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 问候消息通常会路由到__end__
        assert result.goto in ["__end__", "intent_clarification"]
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_supervisor_node_task_request():
    """测试supervisor_node - 任务请求分支 (真实调用)"""
    print("\n🧪 测试 supervisor_node - 任务请求分支")

    # 准备测试数据 - 明确的任务请求
    state = create_test_state(
        session_id="test_task_001",
        messages=[HumanMessage(content="我想分析京东品牌在电商平台的舆情情况，包括用户评价和品牌声誉")],
        workflow_status=WorkflowStatus.INITIALIZING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用supervisor_node
        result = await supervisor_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 路由原因: {result.update.get('reason', 'N/A')}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 任务请求通常会路由到intent_clarification或planning
        assert result.goto in ["intent_clarification", "planning", "execution"]
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_supervisor_node_continue_workflow():
    """测试supervisor_node - 继续工作流分支 (真实调用)"""
    print("\n🧪 测试 supervisor_node - 继续工作流分支")

    # 准备测试数据 - 已有澄清结果的状态
    state = create_test_state(
        session_id="test_continue_001",
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="我理解您想分析京东品牌的舆情。请问您希望重点关注哪些方面？"),
            HumanMessage(content="主要关注用户评价和品牌声誉，时间范围是最近3个月")
        ],
        workflow_status=WorkflowStatus.CLARIFYING_INTENT,
        intent_clarified=True,
        clarification_result={
            "brand": "京东",
            "focus": "用户评价和品牌声誉",
            "time_range": "最近3个月"
        }
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用supervisor_node
        result = await supervisor_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 路由原因: {result.update.get('reason', 'N/A')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 已澄清意图通常会路由到planning
        assert result.goto in ["planning", "execution"]
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# ==================== Intent Clarification Node 测试用例 ====================

async def test_intent_clarification_first_round():
    """测试intent_clarification_node - 首轮澄清 (真实调用)"""
    print("\n🧪 测试 intent_clarification_node - 首轮澄清")

    # 准备测试数据 - 首轮澄清，模糊的需求
    state = create_test_state(
        session_id="test_intent_001",
        messages=[HumanMessage(content="我想分析品牌舆情")],
        clarification_round=0,
        workflow_status=WorkflowStatus.CLARIFYING_INTENT
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用intent_clarification_node
        result = await intent_clarification_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 意图澄清状态: {result.update.get('intent_clarified')}")
        print(f"✅ 澄清轮次: {result.update.get('clarification_round')}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证
        assert result.goto in ["__end__", "planning"]
        assert "clarification_round" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_intent_clarification_user_supplement():
    """测试intent_clarification_node - 用户补充信息分支 (真实调用)"""
    print("\n🧪 测试 intent_clarification_node - 用户补充信息分支")

    # 准备测试数据 - 第二轮澄清，用户提供补充信息
    state = create_test_state(
        session_id="test_intent_002",
        messages=[
            HumanMessage(content="我想分析品牌舆情"),
            AIMessage(content="请提供更多详细信息，比如具体的品牌名称、分析时间范围等。"),
            HumanMessage(content="我想分析京东品牌在最近3个月的舆情情况，重点关注用户评价和品牌声誉")
        ],
        clarification_round=1,
        workflow_status=WorkflowStatus.CLARIFYING_INTENT
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用intent_clarification_node
        result = await intent_clarification_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 意图澄清状态: {result.update.get('intent_clarified')}")
        print(f"✅ 意图批准状态: {result.update.get('intent_approved')}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 补充信息后可能澄清完成或需要进一步澄清
        assert result.goto in ["__end__", "planning"]
        assert "clarification_round" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_intent_clarification_clear_intent():
    """测试intent_clarification_node - 意图明确分支 (真实调用)"""
    print("\n🧪 测试 intent_clarification_node - 意图明确分支")

    # 准备测试数据 - 非常明确的需求
    state = create_test_state(
        session_id="test_intent_003",
        messages=[
            HumanMessage(content="我需要分析京东品牌在2024年1-3月期间的舆情情况，包括用户评价分析、品牌声誉监测、竞品对比分析，重点关注电商平台的用户反馈和社交媒体讨论")
        ],
        clarification_round=0,
        workflow_status=WorkflowStatus.CLARIFYING_INTENT
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用intent_clarification_node
        result = await intent_clarification_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 意图澄清状态: {result.update.get('intent_clarified')}")
        print(f"✅ 意图批准状态: {result.update.get('intent_approved')}")
        print(f"✅ 澄清结果: {result.update.get('clarification_result')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 明确的意图通常直接进入planning
        assert result.goto in ["planning", "__end__"]
        assert "clarification_round" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# ==================== Planning Node 测试用例 ====================

async def test_planning_node_first_plan():
    """测试planning_node - 首次制定计划 (真实调用)"""
    print("\n🧪 测试 planning_node - 首次制定计划")

    # 准备测试数据 - 首次制定计划
    state = create_test_state(
        session_id="test_planning_001",
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="我理解您的需求，现在为您制定详细的执行计划。")
        ],
        planning_round=0,
        intent_clarified=True,
        clarification_result={
            "brand": "京东",
            "analysis_type": "舆情分析",
            "focus_areas": ["用户评价", "品牌声誉"]
        },
        workflow_status=WorkflowStatus.PLANNING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用planning_node
        result = await planning_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 计划批准状态: {result.update.get('plan_approved')}")
        print(f"✅ 计划轮次: {result.update.get('planning_round')}")
        print(f"✅ 任务计划标题: {result.update.get('task_plan', {}).get('title', 'N/A')}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 首次制定计划通常等待用户确认
        assert result.goto == "__end__"
        assert result.update.get('plan_approved') == False
        assert "task_plan" in result.update
        assert "planning_round" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_planning_node_user_approval():
    """测试planning_node - 用户批准计划分支 (真实调用)"""
    print("\n🧪 测试 planning_node - 用户批准计划分支")

    # 准备测试数据 - 用户批准计划
    state = create_test_state(
        session_id="test_planning_002",
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="## 执行计划\n\n1. 数据收集\n2. 数据分析\n3. 报告生成"),
            HumanMessage(content="这个计划很好，我同意执行")
        ],
        planning_round=1,
        plan_approved=False,
        task_plan={
            "title": "京东品牌舆情分析计划",
            "objective": "分析京东品牌舆情状况",
            "steps": [
                {"title": "数据收集", "description": "收集品牌相关数据"},
                {"title": "数据分析", "description": "分析舆情数据"}
            ]
        },
        workflow_status=WorkflowStatus.PLANNING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用planning_node
        result = await planning_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 计划批准状态: {result.update.get('plan_approved')}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 用户批准后进入执行阶段
        assert result.goto in ["execution", "__end__"]
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_planning_node_user_modification():
    """测试planning_node - 用户修改计划分支 (真实调用)"""
    print("\n🧪 测试 planning_node - 用户修改计划分支")

    # 准备测试数据 - 用户要求修改计划
    state = create_test_state(
        session_id="test_planning_003",
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="## 执行计划\n\n1. 数据收集\n2. 数据分析"),
            HumanMessage(content="计划不错，但我希望增加竞品对比分析，并且重点关注社交媒体平台的数据")
        ],
        planning_round=1,
        plan_approved=False,
        task_plan={
            "title": "京东品牌舆情分析计划",
            "steps": [
                {"title": "数据收集", "description": "收集品牌相关数据"},
                {"title": "数据分析", "description": "分析舆情数据"}
            ]
        },
        workflow_status=WorkflowStatus.PLANNING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用planning_node
        result = await planning_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 计划批准状态: {result.update.get('plan_approved')}")
        print(f"✅ 计划轮次: {result.update.get('planning_round')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - 修改建议通常重新制定计划
        assert result.goto == "__end__"
        assert "planning_round" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# ==================== Execution Node 测试用例 ====================

async def test_execution_node_no_plan():
    """测试execution_node - 无执行计划分支 (真实调用)"""
    print("\n🧪 测试 execution_node - 无执行计划分支")

    # 准备测试数据 - 没有task_plan
    state = create_test_state(
        session_id="test_execution_001",
        workflow_status=WorkflowStatus.EXECUTING
        # 故意不提供task_plan
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用execution_node
        result = await execution_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

        # 基本验证 - 无计划应该失败
        assert result.goto == "__end__"
        assert result.update.get('workflow_status') == WorkflowStatus.FAILED
        assert "error_info" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_execution_node_valid_plan():
    """测试execution_node - 有效执行计划分支 (真实调用)"""
    print("\n🧪 测试 execution_node - 有效执行计划分支")

    # 准备测试数据 - 包含有效的task_plan
    task_plan = {
        "title": "京东品牌舆情分析计划",
        "objective": "分析京东品牌舆情状况",
        "steps": [
            {
                "title": "数据收集",
                "description": "收集京东品牌相关的舆情数据，包括用户评价、新闻报道等",
                "estimated_time": "30分钟",
                "skip_execute": False
            },
            {
                "title": "数据分析",
                "description": "对收集的数据进行情感分析和趋势分析",
                "estimated_time": "20分钟",
                "skip_execute": False
            },
            {
                "title": "报告生成",
                "description": "生成分析报告",
                "estimated_time": "10分钟",
                "skip_execute": True  # 跳过执行，由report_node处理
            }
        ],
        "estimated_time": "60分钟"
    }

    state = create_test_state(
        session_id="test_execution_002",
        task_plan=task_plan,
        plan_approved=True,
        workflow_status=WorkflowStatus.EXECUTING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用execution_node
        result = await execution_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 执行报告长度: {len(result.update.get('execution_report', ''))}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印部分writer消息
        for i, msg in enumerate(writer.get_messages()[:5]):  # 只显示前5条
            print(f"  消息{i+1}: {msg}")
        if len(writer.get_messages()) > 5:
            print(f"  ... 还有{len(writer.get_messages()) - 5}条消息")

        # 基本验证 - 成功执行应该进入report阶段
        assert result.goto == "report"
        assert result.update.get('workflow_status') == WorkflowStatus.REPORT
        assert "execution_report" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_execution_node_invalid_plan():
    """测试execution_node - 无效执行计划分支 (真实调用)"""
    print("\n🧪 测试 execution_node - 无效执行计划分支")

    # 准备测试数据 - 无效的task_plan（缺少必要字段）
    task_plan = {
        # 故意缺少title和steps
        "objective": "分析品牌舆情"
    }

    state = create_test_state(
        session_id="test_execution_003",
        task_plan=task_plan,
        workflow_status=WorkflowStatus.EXECUTING
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用execution_node
        result = await execution_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

        # 基本验证 - 无效计划应该失败
        assert result.goto == "__end__"
        assert result.update.get('workflow_status') == WorkflowStatus.FAILED
        assert "error_info" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False





# ==================== Report Node 测试用例 ====================

async def test_report_node_dsl_failed():
    """测试report_node - DSL生成失败分支 (真实调用)"""
    print("\n🧪 测试 report_node - DSL生成失败分支")

    # 准备测试数据 - DSL生成失败
    state = create_test_state(
        session_id="test_report_001",
        workflow_status=WorkflowStatus.REPORT,
        report_dsl_status="FAILED",
        report_dsl_message="DSL生成超时，请稍后重试"
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用report_node
        result = await report_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

        # 打印writer消息
        for i, msg in enumerate(writer.get_messages()):
            print(f"  消息{i+1}: {msg}")

        # 基本验证 - DSL失败应该导致工作流失败
        assert result.goto == "__end__"
        assert result.update.get('workflow_status') == WorkflowStatus.FAILED
        assert "error_info" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_report_node_no_dsl_data():
    """测试report_node - 无DSL数据分支 (真实调用)"""
    print("\n🧪 测试 report_node - 无DSL数据分支")

    # 准备测试数据 - 没有DSL数据，使用默认数据
    state = create_test_state(
        session_id="test_report_002",
        workflow_status=WorkflowStatus.REPORT,
        execution_report="## 执行报告\n\n### 数据收集\n收集了京东品牌相关数据\n\n### 数据分析\n完成了情感分析和趋势分析"
        # 故意不提供report_dsl_data
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用report_node
        result = await report_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 最终报告长度: {len(result.update.get('final_report', ''))}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印部分writer消息
        for i, msg in enumerate(writer.get_messages()[:3]):
            print(f"  消息{i+1}: {msg}")
        if len(writer.get_messages()) > 3:
            print(f"  ... 还有{len(writer.get_messages()) - 3}条消息")

        # 基本验证 - 无DSL数据时使用默认数据，可能成功或失败
        assert result.goto == "__end__"
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_report_node_with_dsl_data():
    """测试report_node - 有DSL数据分支 (真实调用)"""
    print("\n🧪 测试 report_node - 有DSL数据分支")

    # 准备测试数据 - 包含完整的DSL数据
    dsl_data = {
        "ai_summary": "京东品牌在最近3个月的舆情整体表现良好，用户满意度较高。",
        "key_metrics": [
            {"name": "正面评价率", "value": "78%"},
            {"name": "用户满意度", "value": "4.2/5.0"},
            {"name": "品牌提及量", "value": "15,234"}
        ],
        "viewpoints": [
            {"category": "产品质量", "sentiment": "positive", "description": "用户普遍认为产品质量可靠"},
            {"category": "物流服务", "sentiment": "positive", "description": "配送速度和服务质量获得好评"},
            {"category": "价格竞争力", "sentiment": "neutral", "description": "价格水平处于市场中等水平"}
        ],
        "charts_info": [
            {"type": "sentiment_trend", "title": "情感趋势分析"},
            {"type": "keyword_cloud", "title": "关键词云图"}
        ]
    }

    state = create_test_state(
        session_id="test_report_003",
        workflow_status=WorkflowStatus.REPORT,
        report_dsl_data=dsl_data,
        report_dsl_status="SUCCESS",
        execution_report="## 执行报告\n\n### 数据收集\n收集了京东品牌相关数据\n\n### 数据分析\n完成了情感分析和趋势分析"
    )
    writer = MockWriter()
    config = create_base_config()

    try:
        # 真实调用report_node
        result = await report_node(state, writer, config)

        # 验证结果
        print(f"✅ 路由结果: {result.goto}")
        print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
        print(f"✅ 最终报告长度: {len(result.update.get('final_report', ''))}")
        print(f"✅ HTML报告长度: {len(result.update.get('html_report', ''))}")
        print(f"✅ Writer消息数量: {len(writer.get_messages())}")

        # 打印部分writer消息
        for i, msg in enumerate(writer.get_messages()[:3]):
            print(f"  消息{i+1}: {msg}")
        if len(writer.get_messages()) > 3:
            print(f"  ... 还有{len(writer.get_messages()) - 3}条消息")

        # 基本验证 - 有DSL数据时应该尝试生成报告
        assert result.goto == "__end__"
        assert "workflow_status" in result.update

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# ==================== 主测试函数 ====================

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行Workflow节点测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    tests = [
        # Supervisor Node 测试 - 真实调用
        test_supervisor_node_greeting,
        test_supervisor_node_task_request,
        test_supervisor_node_continue_workflow,

        # Intent Clarification Node 测试 - 真实调用
        test_intent_clarification_first_round,
        test_intent_clarification_user_supplement,
        test_intent_clarification_clear_intent,

        # Planning Node 测试 - 真实调用
        test_planning_node_first_plan,
        test_planning_node_user_approval,
        test_planning_node_user_modification,

        # Execution Node 测试 - 真实调用
        test_execution_node_no_plan,
        test_execution_node_valid_plan,
        test_execution_node_invalid_plan,

        # Report Node 测试 - 真实调用
        test_report_node_dsl_failed,
        test_report_node_no_dsl_data,
        test_report_node_with_dsl_data,
    ]
    
    results = []
    for test in tests:
        try:
            print(f"\n{'='*60}")
            result = await test()
            results.append(result)
            print(f"✅ {test.__name__} - 通过")
        except Exception as e:
            print(f"❌ {test.__name__} - 失败: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
