#!/usr/bin/env python3
"""
Workflow节点真实测试用例
测试各个节点的不同分支条件和逻辑
"""

import asyncio
import logging
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入需要的模块
from src.brand_event_agent.workflow import (
    supervisor_node, intent_clarification_node, planning_node, 
    execution_node, report_node, WorkflowState, WorkflowStatus
)
from src.brand_event_agent.config import WorkflowConfiguration
from langchain_core.messages import HumanMessage, AIMessage


class MockWriter:
    """模拟writer，收集所有输出消息"""
    
    def __init__(self):
        self.messages = []
    
    def __call__(self, message):
        self.messages.append(message)
        print(f"Writer: {message}")
    
    def get_messages(self):
        return self.messages
    
    def clear(self):
        self.messages = []


def create_base_config():
    """创建基础配置"""
    return {
        "configurable": {
            "supervisor_model": "gpt-4",
            "intent_model": "gpt-4", 
            "planning_model": "gpt-4",
            "executor_model": "gpt-4",
            "report_base_url": "https://test.example.com",
            "upload_url": "https://test-upload.example.com",
            "report_timeout": 30
        }
    }


def create_test_state(
    session_id: str = "test_session_123",
    messages: List = None,
    workflow_status: WorkflowStatus = WorkflowStatus.INITIALIZING,
    **kwargs
) -> WorkflowState:
    """创建测试用的WorkflowState"""
    
    if messages is None:
        messages = [HumanMessage(content="我想分析京东品牌的舆情情况")]
    
    state = WorkflowState(
        session_id=session_id,
        messages=messages,
        workflow_status=workflow_status,
        **kwargs
    )
    
    return state


# ==================== Supervisor Node 测试用例 ====================

async def test_supervisor_node_greeting():
    """测试supervisor_node - 问候消息分支"""
    print("\n🧪 测试 supervisor_node - 问候消息分支")
    
    # 准备测试数据
    state = create_test_state(
        messages=[HumanMessage(content="你好")]
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock LLM响应
    mock_response = Mock()
    mock_response.message_analysis.message_type = "greeting"
    mock_response.next = "__end__"
    mock_response.workflow_status = WorkflowStatus.INITIALIZING
    mock_response.reason = "用户问候"
    mock_response.response_message = "您好！我是品牌舆情分析助手。"
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_response
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.build_supervisor_routing_prompt') as mock_prompt:
            mock_prompt.return_value = "test prompt"
            
            # 执行测试
            result = await supervisor_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 路由原因: {result.update.get('reason')}")
    print(f"✅ Writer消息数量: {len(writer.get_messages())}")
    
    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.INITIALIZING
    return True


async def test_supervisor_node_task_request():
    """测试supervisor_node - 任务请求分支"""
    print("\n🧪 测试 supervisor_node - 任务请求分支")
    
    # 准备测试数据
    state = create_test_state(
        messages=[HumanMessage(content="我想分析京东品牌的舆情情况")]
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock LLM响应
    mock_response = Mock()
    mock_response.message_analysis.message_type = "task_request"
    mock_response.next = "intent_clarification"
    mock_response.workflow_status = WorkflowStatus.CLARIFYING_INTENT
    mock_response.reason = "需要澄清用户意图"
    mock_response.response_message = None
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_response
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.build_supervisor_routing_prompt') as mock_prompt:
            mock_prompt.return_value = "test prompt"
            
            # 执行测试
            result = await supervisor_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 路由原因: {result.update.get('reason')}")
    
    assert result.goto == "intent_clarification"
    assert result.update.get('workflow_status') == WorkflowStatus.CLARIFYING_INTENT
    return True


async def test_supervisor_node_llm_error():
    """测试supervisor_node - LLM错误分支"""
    print("\n🧪 测试 supervisor_node - LLM错误分支")
    
    # 准备测试数据
    state = create_test_state()
    writer = MockWriter()
    config = create_base_config()
    
    # Mock LLM抛出异常
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.side_effect = Exception("LLM调用失败")
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.build_supervisor_routing_prompt') as mock_prompt:
            mock_prompt.return_value = "test prompt"
            
            # 执行测试
            result = await supervisor_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    
    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.INITIALIZING
    return True


# ==================== Intent Clarification Node 测试用例 ====================

async def test_intent_clarification_first_round():
    """测试intent_clarification_node - 首轮澄清"""
    print("\n🧪 测试 intent_clarification_node - 首轮澄清")
    
    # 准备测试数据
    state = create_test_state(
        clarification_round=0,
        workflow_status=WorkflowStatus.CLARIFYING_INTENT
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock澄清结果 - 需要澄清
    mock_clarification_result = Mock()
    mock_clarification_result.intent_clear = False
    mock_clarification_result.clarification_questions = ["请提供更多详细信息"]
    mock_clarification_result.response_message = "请提供更多关于分析需求的详细信息"
    mock_clarification_result.clarification_result = None
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_clarification_result
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.build_intent_clarification_prompt') as mock_prompt:
            mock_prompt.return_value = "test prompt"
            
            # 执行测试
            result = await intent_clarification_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 意图澄清状态: {result.update.get('intent_clarified')}")
    print(f"✅ 澄清轮次: {result.update.get('clarification_round')}")
    
    assert result.goto == "__end__"
    assert result.update.get('intent_clarified') == False
    assert result.update.get('clarification_round') == 1
    return True


async def test_intent_clarification_user_agreement():
    """测试intent_clarification_node - 用户同意分支"""
    print("\n🧪 测试 intent_clarification_node - 用户同意分支")
    
    # 准备测试数据 - 模拟第二轮澄清，用户同意
    state = create_test_state(
        clarification_round=1,
        workflow_status=WorkflowStatus.CLARIFYING_INTENT,
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="请提供更多详细信息"),
            HumanMessage(content="好的，我同意这个分析计划")
        ]
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock用户意图分析 - 同意
    mock_intent_analysis = Mock()
    mock_intent_analysis.intent_type = "agreement"
    mock_intent_analysis.next_action = "proceed"
    mock_intent_analysis.extracted_info = "用户同意"
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_intent_analysis
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.get_latest_user_response') as mock_response:
            mock_response.return_value = "好的，我同意这个分析计划"
            
            with patch('src.brand_event_agent.workflow.build_intent_analysis_prompt') as mock_prompt:
                mock_prompt.return_value = "test prompt"
                
                # 执行测试
                result = await intent_clarification_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 意图批准状态: {result.update.get('intent_approved')}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    
    assert result.goto == "planning"
    assert result.update.get('intent_approved') == True
    assert result.update.get('workflow_status') == WorkflowStatus.PLANNING
    return True


# ==================== Planning Node 测试用例 ====================

async def test_planning_node_first_plan():
    """测试planning_node - 首次制定计划"""
    print("\n🧪 测试 planning_node - 首次制定计划")
    
    # 准备测试数据
    state = create_test_state(
        planning_round=0,
        intent_clarified=True,
        workflow_status=WorkflowStatus.PLANNING
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock执行计划
    mock_execution_plan = Mock()
    mock_execution_plan.title = "品牌舆情分析计划"
    mock_execution_plan.objective = "分析品牌舆情状况"
    mock_execution_plan.steps = [
        Mock(title="数据收集", description="收集品牌相关数据", estimated_time="30分钟"),
        Mock(title="数据分析", description="分析舆情数据", estimated_time="20分钟")
    ]
    mock_execution_plan.estimated_time = "50分钟"
    mock_execution_plan.model_dump.return_value = {
        "title": "品牌舆情分析计划",
        "objective": "分析品牌舆情状况",
        "steps": [
            {"title": "数据收集", "description": "收集品牌相关数据", "estimated_time": "30分钟"},
            {"title": "数据分析", "description": "分析舆情数据", "estimated_time": "20分钟"}
        ],
        "estimated_time": "50分钟"
    }
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_execution_plan
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.get_clarified_requirements') as mock_requirements:
            mock_requirements.return_value = "分析京东品牌舆情"
            
            with patch('src.brand_event_agent.workflow.build_planning_prompt') as mock_prompt:
                mock_prompt.return_value = "test prompt"
                
                with patch('src.brand_event_agent.workflow.format_plan_message') as mock_format:
                    mock_format.return_value = "## 执行计划\n\n1. 数据收集\n2. 数据分析"
                    
                    # 执行测试
                    result = await planning_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 计划批准状态: {result.update.get('plan_approved')}")
    print(f"✅ 计划轮次: {result.update.get('planning_round')}")
    print(f"✅ 任务计划: {result.update.get('task_plan', {}).get('title', 'N/A')}")
    
    assert result.goto == "__end__"
    assert result.update.get('plan_approved') == False
    assert result.update.get('planning_round') == 1
    assert "task_plan" in result.update
    return True


async def test_planning_node_user_approval():
    """测试planning_node - 用户批准计划分支"""
    print("\n🧪 测试 planning_node - 用户批准计划分支")
    
    # 准备测试数据 - 模拟用户批准计划
    state = create_test_state(
        planning_round=1,
        plan_approved=False,
        workflow_status=WorkflowStatus.PLANNING,
        messages=[
            HumanMessage(content="我想分析京东品牌的舆情情况"),
            AIMessage(content="## 执行计划\n\n1. 数据收集\n2. 数据分析"),
            HumanMessage(content="好的，我同意这个计划")
        ]
    )
    writer = MockWriter()
    config = create_base_config()
    
    # Mock用户反馈分析 - 同意
    mock_feedback_analysis = Mock()
    mock_feedback_analysis.intent_type = "agreement"
    mock_feedback_analysis.next_action = "proceed"
    mock_feedback_analysis.extracted_info = "用户同意计划"
    
    with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_feedback_analysis
        mock_llm_class.return_value = mock_llm
        
        with patch('src.brand_event_agent.workflow.get_latest_plan_feedback') as mock_feedback:
            mock_feedback.return_value = "好的，我同意这个计划"
            
            with patch('src.brand_event_agent.workflow.build_planning_intent_analysis_prompt') as mock_prompt:
                mock_prompt.return_value = "test prompt"
                
                # 执行测试
                result = await planning_node(state, writer, config)
    
    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 计划批准状态: {result.update.get('plan_approved')}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    
    assert result.goto == "execution"
    assert result.update.get('plan_approved') == True
    assert result.update.get('workflow_status') == WorkflowStatus.EXECUTING
    return True


# ==================== Execution Node 测试用例 ====================

async def test_execution_node_no_plan():
    """测试execution_node - 无执行计划分支"""
    print("\n🧪 测试 execution_node - 无执行计划分支")

    # 准备测试数据 - 没有task_plan
    state = create_test_state(
        workflow_status=WorkflowStatus.EXECUTING
    )
    writer = MockWriter()
    config = create_base_config()

    # 执行测试
    result = await execution_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.FAILED
    assert "未提供执行计划" in result.update.get('error_info', {}).get('message', '')
    return True


async def test_execution_node_success():
    """测试execution_node - 成功执行分支"""
    print("\n🧪 测试 execution_node - 成功执行分支")

    # 准备测试数据 - 包含有效的task_plan
    task_plan = {
        "title": "品牌舆情分析计划",
        "objective": "分析品牌舆情状况",
        "steps": [
            {
                "title": "数据收集",
                "description": "收集品牌相关数据",
                "estimated_time": "30分钟",
                "skip_execute": False
            },
            {
                "title": "数据分析",
                "description": "分析舆情数据",
                "estimated_time": "20分钟",
                "skip_execute": False
            },
            {
                "title": "报告生成",
                "description": "生成分析报告",
                "estimated_time": "10分钟",
                "skip_execute": True  # 跳过执行
            }
        ],
        "estimated_time": "60分钟"
    }

    state = create_test_state(
        task_plan=task_plan,
        workflow_status=WorkflowStatus.EXECUTING
    )
    writer = MockWriter()
    config = create_base_config()

    # Mock MCP工具和agent
    with patch('src.brand_event_agent.workflow.create_access_token') as mock_token:
        mock_token.return_value = "test_jwt_token"

        with patch('src.brand_event_agent.workflow.MultiServerMCPClient') as mock_mcp_client:
            mock_client = AsyncMock()
            mock_client.get_tools.return_value = [Mock(name="test_tool")]
            mock_mcp_client.return_value = mock_client

            with patch('src.brand_event_agent.workflow.create_react_agent') as mock_react_agent:
                mock_agent = AsyncMock()
                mock_agent.invoke.return_value = {"output": "步骤执行完成"}
                mock_react_agent.return_value = mock_agent

                with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
                    mock_llm_class.return_value = mock_agent

                    # 执行测试
                    result = await execution_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 执行报告长度: {len(result.update.get('execution_report', ''))}")

    assert result.goto == "report"
    assert result.update.get('workflow_status') == WorkflowStatus.REPORT
    assert "execution_report" in result.update
    return True


async def test_execution_node_mcp_failure():
    """测试execution_node - MCP工具失败分支"""
    print("\n🧪 测试 execution_node - MCP工具失败分支")

    # 准备测试数据
    task_plan = {
        "title": "品牌舆情分析计划",
        "steps": [
            {
                "title": "数据收集",
                "description": "收集品牌相关数据",
                "skip_execute": False
            }
        ]
    }

    state = create_test_state(
        task_plan=task_plan,
        workflow_status=WorkflowStatus.EXECUTING
    )
    writer = MockWriter()
    config = create_base_config()

    # Mock MCP工具失败
    with patch('src.brand_event_agent.workflow.create_access_token') as mock_token:
        mock_token.return_value = "test_jwt_token"

        with patch('src.brand_event_agent.workflow.MultiServerMCPClient') as mock_mcp_client:
            mock_client = AsyncMock()
            mock_client.get_tools.side_effect = Exception("MCP连接失败")
            mock_mcp_client.return_value = mock_client

            with patch('src.brand_event_agent.workflow.ChatOpenAI') as mock_llm_class:
                mock_llm = AsyncMock()
                mock_llm.invoke.return_value = {"output": "使用普通LLM执行"}
                mock_llm_class.return_value = mock_llm

                # 执行测试
                result = await execution_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")

    assert result.goto == "report"
    assert result.update.get('workflow_status') == WorkflowStatus.REPORT
    return True


# ==================== Report Node 测试用例 ====================

async def test_report_node_success():
    """测试report_node - 成功生成报告分支"""
    print("\n🧪 测试 report_node - 成功生成报告分支")

    # 准备测试数据
    state = create_test_state(
        workflow_status=WorkflowStatus.REPORT,
        execution_report="## 执行报告\n\n数据收集完成\n数据分析完成"
    )
    writer = MockWriter()
    config = create_base_config()

    # Mock HTTP响应
    mock_response = AsyncMock()
    mock_response.status_code = 200
    mock_response.text = "<html><body>测试报告</body></html>"

    mock_upload_response = AsyncMock()
    mock_upload_response.status_code = 200
    mock_upload_response.json.return_value = {
        "fileKey": "report-test.html",
        "url": "https://example.com/report.html"
    }

    with patch('httpx.AsyncClient') as mock_client_class:
        mock_client = AsyncMock()
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        mock_client.post.side_effect = [mock_response, mock_upload_response]
        mock_client_class.return_value = mock_client

        with patch('tempfile.NamedTemporaryFile') as mock_temp_file:
            mock_file = Mock()
            mock_file.name = "/tmp/test_report.html"
            mock_file.__enter__.return_value = mock_file
            mock_file.__exit__.return_value = None
            mock_temp_file.return_value = mock_file

            with patch('builtins.open', create=True) as mock_open:
                mock_open.return_value.__enter__.return_value = Mock()

                with patch('os.path.exists') as mock_exists:
                    mock_exists.return_value = True

                    with patch('os.unlink') as mock_unlink:
                        # 执行测试
                        result = await report_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 最终报告长度: {len(result.update.get('final_report', ''))}")
    print(f"✅ HTML报告长度: {len(result.update.get('html_report', ''))}")

    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.INITIALIZING
    assert "final_report" in result.update
    assert "html_report" in result.update
    return True


async def test_report_node_dsl_failed():
    """测试report_node - DSL生成失败分支"""
    print("\n🧪 测试 report_node - DSL生成失败分支")

    # 准备测试数据 - DSL生成失败
    state = create_test_state(
        workflow_status=WorkflowStatus.REPORT,
        report_dsl_status="FAILED",
        report_dsl_message="DSL生成超时"
    )
    writer = MockWriter()
    config = create_base_config()

    # 执行测试
    result = await report_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.FAILED
    assert "DSL生成失败" in result.update.get('error_info', {}).get('message', '')
    return True


async def test_report_node_service_failure():
    """测试report_node - 报告服务失败分支"""
    print("\n🧪 测试 report_node - 报告服务失败分支")

    # 准备测试数据
    state = create_test_state(
        workflow_status=WorkflowStatus.REPORT
    )
    writer = MockWriter()
    config = create_base_config()

    # Mock HTTP响应失败
    mock_response = AsyncMock()
    mock_response.status_code = 500
    mock_response.text = "Internal Server Error"

    with patch('httpx.AsyncClient') as mock_client_class:
        mock_client = AsyncMock()
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        mock_client.post.return_value = mock_response
        mock_client_class.return_value = mock_client

        # 执行测试
        result = await report_node(state, writer, config)

    # 验证结果
    print(f"✅ 路由结果: {result.goto}")
    print(f"✅ 工作流状态: {result.update.get('workflow_status')}")
    print(f"✅ 错误信息: {result.update.get('error_info', {}).get('message', 'N/A')}")

    assert result.goto == "__end__"
    assert result.update.get('workflow_status') == WorkflowStatus.FAILED
    assert "报告生成失败" in result.update.get('error_info', {}).get('message', '')
    return True


# ==================== 主测试函数 ====================

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行Workflow节点测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    tests = [
        # Supervisor Node 测试
        test_supervisor_node_greeting,
        test_supervisor_node_task_request,
        test_supervisor_node_llm_error,
        
        # Intent Clarification Node 测试
        test_intent_clarification_first_round,
        test_intent_clarification_user_agreement,
        
        # Planning Node 测试
        test_planning_node_first_plan,
        test_planning_node_user_approval,
    ]
    
    results = []
    for test in tests:
        try:
            print(f"\n{'='*60}")
            result = await test()
            results.append(result)
            print(f"✅ {test.__name__} - 通过")
        except Exception as e:
            print(f"❌ {test.__name__} - 失败: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
