#!/usr/bin/env python3
"""
测试i18n消息系统
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 直接导入messages模块，避免其他依赖问题
try:
    from src.brand_event_agent.messages import (
        get_message, agent_message, live_status_message, human_feedback_message,
        msg, agent_msg, status_msg, feedback_msg, set_language, MessageManager
    )
except ImportError as e:
    print(f"导入错误: {e}")
    print("尝试直接执行messages.py文件...")

    # 直接执行messages.py来测试
    exec(open('src/brand_event_agent/messages.py').read())

    # 重新导入
    from src.brand_event_agent.messages import (
        get_message, agent_message, live_status_message, human_feedback_message,
        msg, agent_msg, status_msg, feedback_msg, set_language, MessageManager
    )


def test_basic_message_retrieval():
    """测试基本消息获取"""
    print("🧪 测试基本消息获取...")
    
    try:
        # 测试简单消息获取
        message = get_message("supervisor.analyzing_state")
        assert message == "正在分析当前状态和用户消息...", f"消息不匹配: {message}"
        print("✅ 简单消息获取成功")
        
        # 测试嵌套消息获取
        message = get_message("intent.analyzing_requirements")
        assert "正在分析您的需求" in message, f"嵌套消息不匹配: {message}"
        print("✅ 嵌套消息获取成功")
        
        # 测试不存在的消息键
        message = get_message("nonexistent.key")
        assert message == "[nonexistent.key]", f"Fallback机制失败: {message}"
        print("✅ Fallback机制正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本消息获取测试失败: {e}")
        return False


def test_parameterized_messages():
    """测试参数化消息"""
    print("\n🧪 测试参数化消息...")
    
    try:
        # 测试单参数消息
        message = get_message("execution.executing_step", step_name="数据收集")
        expected = "开始执行数据收集..."
        assert message == expected, f"单参数消息不匹配: {message}"
        print("✅ 单参数消息格式化成功")
        
        # 测试多参数消息
        message = get_message("execution.step_start", step_index=1, step_title="品牌分析")
        expected = "接下来我将执行第1个任务：品牌分析"
        assert message == expected, f"多参数消息不匹配: {message}"
        print("✅ 多参数消息格式化成功")
        
        # 测试错误消息参数化
        message = get_message("errors.step_failed", error="网络连接失败")
        expected = "执行失败：网络连接失败"
        assert message == expected, f"错误消息参数化失败: {message}"
        print("✅ 错误消息参数化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数化消息测试失败: {e}")
        return False


def test_message_format_functions():
    """测试消息格式化函数"""
    print("\n🧪 测试消息格式化函数...")
    
    try:
        # 测试agent_message函数
        result = agent_message("supervisor.analyzing_state")
        expected = {"agent_message": "正在分析当前状态和用户消息..."}
        assert result == expected, f"agent_message函数失败: {result}"
        print("✅ agent_message函数正常")
        
        # 测试live_status_message函数
        result = live_status_message("intent.analyzing")
        expected = {"live_status_message": "正在分析用户意图..."}
        assert result == expected, f"live_status_message函数失败: {result}"
        print("✅ live_status_message函数正常")
        
        # 测试human_feedback_message函数
        result = human_feedback_message("intent.need_more_info")
        expected = {"human_feedback_message": "请提供更多详细信息，以便我为您制定最佳的分析方案。"}
        assert result == expected, f"human_feedback_message函数失败: {result}"
        print("✅ human_feedback_message函数正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 消息格式化函数测试失败: {e}")
        return False


def test_convenience_aliases():
    """测试便捷别名函数"""
    print("\n🧪 测试便捷别名函数...")
    
    try:
        # 测试msg别名
        message1 = msg("supervisor.analyzing_state")
        message2 = get_message("supervisor.analyzing_state")
        assert message1 == message2, "msg别名函数失败"
        print("✅ msg别名函数正常")
        
        # 测试agent_msg别名
        result1 = agent_msg("supervisor.analyzing_state")
        result2 = agent_message("supervisor.analyzing_state")
        assert result1 == result2, "agent_msg别名函数失败"
        print("✅ agent_msg别名函数正常")
        
        # 测试status_msg别名
        result1 = status_msg("intent.analyzing")
        result2 = live_status_message("intent.analyzing")
        assert result1 == result2, "status_msg别名函数失败"
        print("✅ status_msg别名函数正常")
        
        # 测试feedback_msg别名
        result1 = feedback_msg("intent.need_more_info")
        result2 = human_feedback_message("intent.need_more_info")
        assert result1 == result2, "feedback_msg别名函数失败"
        print("✅ feedback_msg别名函数正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷别名函数测试失败: {e}")
        return False


def test_language_switching():
    """测试语言切换"""
    print("\n🧪 测试语言切换...")
    
    try:
        # 默认中文
        message_zh = get_message("supervisor.analyzing_state")
        assert "正在分析" in message_zh, f"中文消息错误: {message_zh}"
        print("✅ 默认中文消息正常")
        
        # 切换到英文
        set_language("en_US")
        message_en = get_message("supervisor.analyzing_state")
        assert "Analyzing" in message_en, f"英文消息错误: {message_en}"
        print("✅ 英文消息切换成功")
        
        # 验证消息不同
        assert message_zh != message_en, "语言切换后消息应该不同"
        print("✅ 语言切换验证成功")
        
        # 切换回中文
        set_language("zh_CN")
        message_zh_back = get_message("supervisor.analyzing_state")
        assert message_zh_back == message_zh, "切换回中文失败"
        print("✅ 切换回中文成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 语言切换测试失败: {e}")
        return False


def test_message_manager_class():
    """测试MessageManager类"""
    print("\n🧪 测试MessageManager类...")
    
    try:
        # 创建中文管理器
        manager_zh = MessageManager("zh_CN")
        message_zh = manager_zh.get_message("supervisor.analyzing_state")
        assert "正在分析" in message_zh, f"中文管理器失败: {message_zh}"
        print("✅ 中文MessageManager正常")
        
        # 创建英文管理器
        manager_en = MessageManager("en_US")
        message_en = manager_en.get_message("supervisor.analyzing_state")
        assert "Analyzing" in message_en, f"英文管理器失败: {message_en}"
        print("✅ 英文MessageManager正常")
        
        # 测试管理器方法
        agent_result = manager_zh.agent_message("supervisor.analyzing_state")
        assert "agent_message" in agent_result, "agent_message方法失败"
        print("✅ MessageManager方法正常")
        
        return True
        
    except Exception as e:
        print(f"❌ MessageManager类测试失败: {e}")
        return False


def test_real_world_usage():
    """测试真实使用场景"""
    print("\n🧪 测试真实使用场景...")
    
    try:
        # 模拟writer函数
        messages_sent = []
        
        def mock_writer(message_dict):
            messages_sent.append(message_dict)
        
        # 模拟supervisor节点使用
        mock_writer(status_msg("supervisor.analyzing_state"))
        mock_writer(agent_msg("supervisor.greeting_help"))
        
        # 验证消息
        assert len(messages_sent) == 2, f"应该发送2条消息，实际: {len(messages_sent)}"
        assert "live_status_message" in messages_sent[0], "第一条应该是状态消息"
        assert "agent_message" in messages_sent[1], "第二条应该是agent消息"
        print("✅ supervisor节点使用场景正常")
        
        # 模拟execution节点使用
        messages_sent.clear()
        step_index = 1
        step_title = "数据收集"
        result = "收集完成"
        
        mock_writer(agent_msg("execution.step_start", step_index=step_index, step_title=step_title))
        mock_writer(agent_msg("execution.step_completed", step_index=step_index, result=result))
        
        # 验证参数化消息
        assert len(messages_sent) == 2, f"应该发送2条消息，实际: {len(messages_sent)}"
        step_start_msg = messages_sent[0]["agent_message"]
        assert "第1个任务" in step_start_msg and "数据收集" in step_start_msg, f"步骤开始消息错误: {step_start_msg}"
        print("✅ execution节点使用场景正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实使用场景测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试i18n消息系统")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_basic_message_retrieval,
        test_parameterized_messages,
        test_message_format_functions,
        test_convenience_aliases,
        test_language_switching,
        test_message_manager_class,
        test_real_world_usage,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！i18n消息系统工作正常")
        print("\n✅ 功能验证:")
        print("  - 基本消息获取 ✓")
        print("  - 参数化消息 ✓")
        print("  - 消息格式化函数 ✓")
        print("  - 便捷别名函数 ✓")
        print("  - 语言切换 ✓")
        print("  - MessageManager类 ✓")
        print("  - 真实使用场景 ✓")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
