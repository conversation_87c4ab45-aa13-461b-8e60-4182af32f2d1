#!/usr/bin/env python3
"""
Brand Event Agent API使用示例
展示如何使用新的API接口进行品牌舆情分析
"""

import asyncio
import json
from typing import Dict, Any

# 导入API类
from src.brand_event_agent.api import BrandEventAPI, ChatRequest, Extensions, ServiceToken
from src.brand_event_agent.workflow import WorkflowStatus


async def example_basic_chat():
    """基本聊天示例"""
    print("🔧 基本聊天示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    # 创建聊天请求
    request = ChatRequest(
        message="我想分析京东品牌的舆情情况",
        session_id="example_session_001",
        user_id="test_user",
        task_id="task_001",
        sandbox_id="sandbox_001"
    )
    
    # 发送请求
    response = await api.chat(request)
    
    print(f"✅ 会话ID: {response.session_id}")
    print(f"✅ 响应: {response.response}")
    print(f"✅ 状态: {response.status}")
    print(f"✅ 需要反馈: {response.requires_feedback}")
    print(f"✅ 元数据: {response.metadata}")
    
    return response


async def example_with_extensions():
    """带扩展字段的示例"""
    print("\n⚙️ 带扩展字段的示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    # 创建扩展字段
    extensions = Extensions(
        tokens=[
            ServiceToken(service_id="service_001", access_token="token_123"),
            ServiceToken(service_id="service_002", access_token="token_456")
        ],
        additional_fields={
            "priority": "high",
            "department": "marketing",
            "analysis_type": "comprehensive"
        }
    )
    
    # 创建聊天请求
    request = ChatRequest(
        message="请分析京东品牌在最近3个月的舆情变化趋势",
        session_id="example_session_002",
        user_id="marketing_user",
        task_id="task_002",
        sandbox_id="sandbox_002",
        event_webhook="https://webhook.example.com/events",
        extensions=extensions
    )
    
    # 发送请求
    response = await api.chat(request)
    
    print(f"✅ 会话ID: {response.session_id}")
    print(f"✅ 响应: {response.response}")
    print(f"✅ 状态: {response.status}")
    print(f"✅ 扩展字段: {response.extensions}")
    
    return response


async def example_with_report_dsl():
    """带报表DSL数据的示例"""
    print("\n📊 带报表DSL数据的示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    # 模拟报表DSL数据
    report_dsl_data = {
        "ai_summary": "京东品牌在最近3个月的舆情整体表现良好，用户满意度较高。",
        "key_metrics": [
            {"name": "正面评价率", "value": "78%"},
            {"name": "用户满意度", "value": "4.2/5.0"},
            {"name": "品牌提及量", "value": "15,234"}
        ],
        "viewpoints": [
            {"category": "产品质量", "sentiment": "positive", "description": "用户普遍认为产品质量可靠"},
            {"category": "物流服务", "sentiment": "positive", "description": "配送速度和服务质量获得好评"},
            {"category": "价格竞争力", "sentiment": "neutral", "description": "价格水平处于市场中等水平"}
        ],
        "charts_info": [
            {"type": "sentiment_trend", "title": "情感趋势分析"},
            {"type": "keyword_cloud", "title": "关键词云图"}
        ]
    }
    
    # 创建聊天请求
    request = ChatRequest(
        message="请基于提供的DSL数据生成品牌舆情分析报告",
        session_id="example_session_003",
        user_id="analyst_user",
        task_id="task_003",
        report_dsl_data=report_dsl_data,
        report_dsl_status="SUCCESS",
        workflow_status=WorkflowStatus.REPORT
    )
    
    # 发送请求
    response = await api.chat(request)
    
    print(f"✅ 会话ID: {response.session_id}")
    print(f"✅ 响应: {response.response}")
    print(f"✅ 状态: {response.status}")
    print(f"✅ 工作流状态: {response.metadata.get('workflow_status')}")
    
    return response


async def example_continue_session():
    """继续现有会话的示例"""
    print("\n🔄 继续现有会话的示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    # 第一次请求 - 开始分析
    request1 = ChatRequest(
        message="我想分析小米品牌的舆情情况",
        session_id="continue_session_001",
        user_id="test_user"
    )
    
    response1 = await api.chat(request1)
    print(f"第一次响应: {response1.response}")
    
    # 第二次请求 - 继续对话
    request2 = ChatRequest(
        message="我同意这个分析计划，请开始执行",
        session_id="continue_session_001",  # 使用相同的session_id
        user_id="test_user"
    )
    
    response2 = await api.chat(request2)
    print(f"第二次响应: {response2.response}")
    
    return response1, response2


async def example_session_management():
    """会话管理示例"""
    print("\n📋 会话管理示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    # 创建几个会话
    sessions = []
    for i in range(3):
        request = ChatRequest(
            message=f"分析品牌{i+1}的舆情情况",
            session_id=f"session_{i+1:03d}",
            user_id=f"user_{i+1}"
        )
        response = await api.chat(request)
        sessions.append(response.session_id)
    
    # 获取系统状态
    system_status = api.get_system_status()
    print(f"✅ 系统状态: {system_status.status}")
    print(f"✅ 活跃会话数: {system_status.active_sessions}")
    print(f"✅ 总会话数: {system_status.total_sessions}")
    
    # 获取会话信息
    for session_id in sessions:
        session_info = api.get_session_info(session_id)
        if session_info:
            print(f"✅ 会话 {session_id}: 用户={session_info.user_id}, 消息数={session_info.message_count}")
    
    # 健康检查
    health = api.health_check()
    print(f"✅ 健康状态: {health.status}")
    print(f"✅ 服务状态: {health.services}")
    
    return system_status, health


async def example_error_handling():
    """错误处理示例"""
    print("\n⚠️ 错误处理示例")
    print("=" * 50)
    
    # 创建API实例
    api = BrandEventAPI()
    
    try:
        # 创建一个可能导致错误的请求
        request = ChatRequest(
            message="",  # 空消息
            session_id="error_session_001"
        )
        
        response = await api.chat(request)
        print(f"✅ 响应: {response.response}")
        print(f"✅ 状态: {response.status}")
        
    except Exception as e:
        print(f"❌ 捕获到错误: {e}")
    
    # 测试不存在的会话
    non_existent_session = api.get_session_info("non_existent_session")
    print(f"✅ 不存在的会话: {non_existent_session}")
    
    return True


async def main():
    """主函数"""
    print("🚀 Brand Event Agent API 使用示例")
    print("=" * 60)
    
    # 运行所有示例
    examples = [
        example_basic_chat,
        example_with_extensions,
        example_with_report_dsl,
        example_continue_session,
        example_session_management,
        example_error_handling,
    ]
    
    for example in examples:
        try:
            await example()
        except Exception as e:
            print(f"❌ 示例 {example.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("✅ API功能特点:")
    print("   1. 兼容现有ChatRequest/ChatResponse格式")
    print("   2. 支持扩展字段和服务token")
    print("   3. 支持报表DSL数据传递")
    print("   4. 支持工作流状态控制")
    print("   5. 完整的会话管理功能")
    print("   6. 流式处理支持")
    print("   7. 错误处理和健康检查")
    
    print("\n🔧 使用方法:")
    print("   # 基本使用")
    print("   api = BrandEventAPI()")
    print("   request = ChatRequest(message='分析品牌舆情')")
    print("   response = await api.chat(request)")
    print()
    print("   # 带扩展字段")
    print("   extensions = Extensions(tokens=[...], additional_fields={...})")
    print("   request = ChatRequest(message='...', extensions=extensions)")
    print()
    print("   # 会话管理")
    print("   session_info = api.get_session_info(session_id)")
    print("   system_status = api.get_system_status()")


if __name__ == "__main__":
    asyncio.run(main())
