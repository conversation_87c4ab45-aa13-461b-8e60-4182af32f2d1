#!/usr/bin/env python3
"""
简单的i18n消息系统测试
"""

# 直接复制messages.py的核心代码进行测试
import os
from typing import Dict, Any, Optional

# 默认语言
DEFAULT_LANGUAGE = "zh_CN"

# 消息配置
MESSAGES = {
    "zh_CN": {
        "supervisor": {
            "analyzing_state": "正在分析当前状态和用户消息...",
            "greeting_help": "请输入具体任务需求，我将为您提供帮助。"
        },
        "intent": {
            "analyzing": "正在分析用户意图...",
            "need_more_info": "请提供更多详细信息，以便我为您制定最佳的分析方案。"
        },
        "execution": {
            "step_start": "接下来我将执行第{step_index}个任务：{step_title}",
            "step_completed": "第{step_index}个任务已完成：{result}"
        },
        "errors": {
            "step_failed": "执行失败：{error}"
        }
    },
    "en_US": {
        "supervisor": {
            "analyzing_state": "Analyzing current state and user messages...",
            "greeting_help": "Please enter specific task requirements, I will provide assistance."
        },
        "intent": {
            "analyzing": "Analyzing user intent...",
            "need_more_info": "Please provide more detailed information so I can create the best analysis plan for you."
        },
        "execution": {
            "step_start": "Next I will execute task {step_index}: {step_title}",
            "step_completed": "Task {step_index} completed: {result}"
        },
        "errors": {
            "step_failed": "Execution failed: {error}"
        }
    }
}


class MessageManager:
    """消息管理器"""
    
    def __init__(self, language: str = None):
        self.language = language or DEFAULT_LANGUAGE
    
    def get_message(self, key: str, **kwargs) -> str:
        """获取消息"""
        try:
            keys = key.split(".")
            messages = MESSAGES.get(self.language, MESSAGES[DEFAULT_LANGUAGE])
            
            current = messages
            for k in keys:
                current = current[k]
            
            if isinstance(current, str):
                return current.format(**kwargs)
            else:
                return str(current)
                
        except (KeyError, TypeError, ValueError) as e:
            return f"[{key}]"
    
    def agent_message(self, key: str, **kwargs) -> Dict[str, str]:
        """生成agent消息格式"""
        return {"agent_message": self.get_message(key, **kwargs)}
    
    def live_status_message(self, key: str, **kwargs) -> Dict[str, str]:
        """生成实时状态消息格式"""
        return {"live_status_message": self.get_message(key, **kwargs)}


# 全局消息管理器实例
_message_manager = MessageManager()


def get_message(key: str, **kwargs) -> str:
    """获取消息的便捷函数"""
    return _message_manager.get_message(key, **kwargs)


def agent_message(key: str, **kwargs) -> Dict[str, str]:
    """生成agent消息的便捷函数"""
    return _message_manager.agent_message(key, **kwargs)


def live_status_message(key: str, **kwargs) -> Dict[str, str]:
    """生成实时状态消息的便捷函数"""
    return _message_manager.live_status_message(key, **kwargs)


def set_language(language: str):
    """设置全局语言"""
    global _message_manager
    _message_manager = MessageManager(language)


# 便捷别名
msg = get_message
agent_msg = agent_message
status_msg = live_status_message


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    # 测试简单消息获取
    message = get_message("supervisor.analyzing_state")
    print(f"中文消息: {message}")
    assert "正在分析" in message, f"中文消息错误: {message}"
    
    # 测试参数化消息
    message = get_message("execution.step_start", step_index=1, step_title="数据收集")
    print(f"参数化消息: {message}")
    assert "第1个任务" in message and "数据收集" in message, f"参数化消息错误: {message}"
    
    # 测试消息格式化
    result = agent_message("supervisor.analyzing_state")
    print(f"Agent消息格式: {result}")
    assert "agent_message" in result, f"Agent消息格式错误: {result}"
    
    result = live_status_message("intent.analyzing")
    print(f"状态消息格式: {result}")
    assert "live_status_message" in result, f"状态消息格式错误: {result}"
    
    print("✅ 基本功能测试通过")


def test_language_switching():
    """测试语言切换"""
    print("\n🧪 测试语言切换...")
    
    # 默认中文
    message_zh = get_message("supervisor.analyzing_state")
    print(f"中文: {message_zh}")
    
    # 切换到英文
    set_language("en_US")
    message_en = get_message("supervisor.analyzing_state")
    print(f"英文: {message_en}")
    
    # 验证不同
    assert message_zh != message_en, "语言切换后消息应该不同"
    assert "Analyzing" in message_en, f"英文消息错误: {message_en}"
    
    # 切换回中文
    set_language("zh_CN")
    message_zh_back = get_message("supervisor.analyzing_state")
    assert message_zh_back == message_zh, "切换回中文失败"
    
    print("✅ 语言切换测试通过")


def test_convenience_functions():
    """测试便捷函数"""
    print("\n🧪 测试便捷函数...")
    
    # 测试别名函数
    message1 = msg("supervisor.analyzing_state")
    message2 = get_message("supervisor.analyzing_state")
    assert message1 == message2, "msg别名函数失败"
    
    result1 = agent_msg("supervisor.analyzing_state")
    result2 = agent_message("supervisor.analyzing_state")
    assert result1 == result2, "agent_msg别名函数失败"
    
    result1 = status_msg("intent.analyzing")
    result2 = live_status_message("intent.analyzing")
    assert result1 == result2, "status_msg别名函数失败"
    
    print("✅ 便捷函数测试通过")


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    # 测试不存在的键
    message = get_message("nonexistent.key")
    assert message == "[nonexistent.key]", f"Fallback机制失败: {message}"
    
    # 测试参数格式化错误
    message = get_message("execution.step_start", step_index=1)  # 缺少step_title参数
    print(f"参数缺失时的消息: {message}")
    # 应该返回fallback
    assert "[execution.step_start]" in message, f"参数错误处理失败: {message}"
    
    print("✅ 错误处理测试通过")


def test_real_usage_scenario():
    """测试真实使用场景"""
    print("\n🧪 测试真实使用场景...")
    
    # 模拟workflow中的使用
    messages_sent = []
    
    def mock_writer(message_dict):
        messages_sent.append(message_dict)
        print(f"发送消息: {message_dict}")
    
    # 模拟supervisor节点
    mock_writer(status_msg("supervisor.analyzing_state"))
    mock_writer(agent_msg("supervisor.greeting_help"))
    
    # 模拟execution节点
    for step_index in range(1, 3):
        step_title = f"步骤{step_index}"
        mock_writer(agent_msg("execution.step_start", step_index=step_index, step_title=step_title))
        
        result = f"步骤{step_index}完成"
        mock_writer(agent_msg("execution.step_completed", step_index=step_index, result=result))
    
    # 验证消息数量
    assert len(messages_sent) == 6, f"应该发送6条消息，实际: {len(messages_sent)}"
    
    # 验证消息类型
    assert "live_status_message" in messages_sent[0], "第一条应该是状态消息"
    assert "agent_message" in messages_sent[1], "第二条应该是agent消息"
    
    print("✅ 真实使用场景测试通过")


def main():
    """主测试函数"""
    print("🚀 开始测试i18n消息系统")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_language_switching()
        test_convenience_functions()
        test_error_handling()
        test_real_usage_scenario()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！i18n消息系统工作正常")
        print("\n✅ 实现特性:")
        print("  - 多语言支持 (中文/英文)")
        print("  - 参数化消息")
        print("  - 消息格式化函数")
        print("  - 便捷别名函数")
        print("  - 错误处理和Fallback机制")
        print("  - 语言动态切换")
        print("  - 真实使用场景验证")
        
        print("\n📝 使用方法:")
        print("  from messages import agent_msg, status_msg, msg")
        print("  writer(agent_msg('supervisor.analyzing_state'))")
        print("  writer(status_msg('execution.step_start', step_index=1, step_title='数据收集'))")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
