# 🧪 Workflow节点测试指南

## 📋 测试概述

为每个workflow节点创建了真实调用的测试用例，覆盖不同的分支条件和业务逻辑。

### 🎯 测试特点

- **真实调用**: 只mock writer，其他组件使用真实调用
- **分支覆盖**: 每个节点测试不同的分支条件
- **状态驱动**: 通过不同的WorkflowState测试不同场景
- **实际验证**: 验证真实的路由结果和状态更新

## 📁 测试文件

- `test_workflow_nodes.py` - 完整的测试用例集合
- `run_workflow_tests.py` - 简化的测试运行脚本

## 🔧 运行方式

### 运行所有关键测试
```bash
python run_workflow_tests.py
```

### 运行单个测试
```bash
python run_workflow_tests.py supervisor_greeting
python run_workflow_tests.py execution_valid
```

### 查看帮助
```bash
python run_workflow_tests.py --help
```

## 📊 测试用例详情

### 1. Supervisor Node 测试

#### test_supervisor_node_greeting
**测试场景**: 用户问候消息
**State配置**:
```python
state = create_test_state(
    session_id="test_greeting_001",
    messages=[HumanMessage(content="你好")],
    workflow_status=WorkflowStatus.INITIALIZING
)
```
**预期结果**: 路由到 `__end__` 或 `intent_clarification`

#### test_supervisor_node_task_request
**测试场景**: 明确的任务请求
**State配置**:
```python
state = create_test_state(
    session_id="test_task_001",
    messages=[HumanMessage(content="我想分析京东品牌在电商平台的舆情情况，包括用户评价和品牌声誉")],
    workflow_status=WorkflowStatus.INITIALIZING
)
```
**预期结果**: 路由到 `intent_clarification`、`planning` 或 `execution`

#### test_supervisor_node_continue_workflow
**测试场景**: 继续现有工作流
**State配置**:
```python
state = create_test_state(
    session_id="test_continue_001",
    messages=[...], # 包含历史对话
    workflow_status=WorkflowStatus.CLARIFYING_INTENT,
    intent_clarified=True,
    clarification_result={...}
)
```
**预期结果**: 路由到 `planning` 或 `execution`

### 2. Intent Clarification Node 测试

#### test_intent_clarification_first_round
**测试场景**: 首轮意图澄清
**State配置**:
```python
state = create_test_state(
    session_id="test_intent_001",
    messages=[HumanMessage(content="我想分析品牌舆情")],
    clarification_round=0,
    workflow_status=WorkflowStatus.CLARIFYING_INTENT
)
```
**预期结果**: 路由到 `__end__` 或 `planning`，澄清轮次+1

#### test_intent_clarification_user_supplement
**测试场景**: 用户提供补充信息
**State配置**:
```python
state = create_test_state(
    session_id="test_intent_002",
    messages=[
        HumanMessage(content="我想分析品牌舆情"),
        AIMessage(content="请提供更多详细信息..."),
        HumanMessage(content="我想分析京东品牌在最近3个月的舆情情况...")
    ],
    clarification_round=1,
    workflow_status=WorkflowStatus.CLARIFYING_INTENT
)
```
**预期结果**: 路由到 `__end__` 或 `planning`

#### test_intent_clarification_clear_intent
**测试场景**: 意图非常明确
**State配置**:
```python
state = create_test_state(
    session_id="test_intent_003",
    messages=[HumanMessage(content="我需要分析京东品牌在2024年1-3月期间的舆情情况...")],
    clarification_round=0,
    workflow_status=WorkflowStatus.CLARIFYING_INTENT
)
```
**预期结果**: 直接路由到 `planning`

### 3. Planning Node 测试

#### test_planning_node_first_plan
**测试场景**: 首次制定计划
**State配置**:
```python
state = create_test_state(
    session_id="test_planning_001",
    planning_round=0,
    intent_clarified=True,
    clarification_result={...},
    workflow_status=WorkflowStatus.PLANNING
)
```
**预期结果**: 路由到 `__end__`，等待用户确认

#### test_planning_node_user_approval
**测试场景**: 用户批准计划
**State配置**:
```python
state = create_test_state(
    session_id="test_planning_002",
    messages=[..., HumanMessage(content="这个计划很好，我同意执行")],
    planning_round=1,
    plan_approved=False,
    task_plan={...},
    workflow_status=WorkflowStatus.PLANNING
)
```
**预期结果**: 路由到 `execution`

#### test_planning_node_user_modification
**测试场景**: 用户要求修改计划
**State配置**:
```python
state = create_test_state(
    session_id="test_planning_003",
    messages=[..., HumanMessage(content="计划不错，但我希望增加竞品对比分析...")],
    planning_round=1,
    plan_approved=False,
    task_plan={...},
    workflow_status=WorkflowStatus.PLANNING
)
```
**预期结果**: 路由到 `__end__`，重新制定计划

### 4. Execution Node 测试

#### test_execution_node_no_plan
**测试场景**: 没有执行计划
**State配置**:
```python
state = create_test_state(
    session_id="test_execution_001",
    workflow_status=WorkflowStatus.EXECUTING
    # 故意不提供task_plan
)
```
**预期结果**: 路由到 `__end__`，状态为 `FAILED`

#### test_execution_node_valid_plan
**测试场景**: 有效的执行计划
**State配置**:
```python
state = create_test_state(
    session_id="test_execution_002",
    task_plan={
        "title": "京东品牌舆情分析计划",
        "steps": [
            {"title": "数据收集", "skip_execute": False},
            {"title": "数据分析", "skip_execute": False},
            {"title": "报告生成", "skip_execute": True}
        ]
    },
    plan_approved=True,
    workflow_status=WorkflowStatus.EXECUTING
)
```
**预期结果**: 路由到 `report`，状态为 `REPORT`

#### test_execution_node_invalid_plan
**测试场景**: 无效的执行计划
**State配置**:
```python
state = create_test_state(
    session_id="test_execution_003",
    task_plan={"objective": "分析品牌舆情"}, # 缺少必要字段
    workflow_status=WorkflowStatus.EXECUTING
)
```
**预期结果**: 路由到 `__end__`，状态为 `FAILED`

### 5. Report Node 测试

#### test_report_node_dsl_failed
**测试场景**: DSL生成失败
**State配置**:
```python
state = create_test_state(
    session_id="test_report_001",
    workflow_status=WorkflowStatus.REPORT,
    report_dsl_status="FAILED",
    report_dsl_message="DSL生成超时，请稍后重试"
)
```
**预期结果**: 路由到 `__end__`，状态为 `FAILED`

#### test_report_node_no_dsl_data
**测试场景**: 没有DSL数据，使用默认数据
**State配置**:
```python
state = create_test_state(
    session_id="test_report_002",
    workflow_status=WorkflowStatus.REPORT,
    execution_report="## 执行报告\n\n..."
    # 故意不提供report_dsl_data
)
```
**预期结果**: 路由到 `__end__`，尝试使用默认数据生成报告

#### test_report_node_with_dsl_data
**测试场景**: 包含完整DSL数据
**State配置**:
```python
state = create_test_state(
    session_id="test_report_003",
    workflow_status=WorkflowStatus.REPORT,
    report_dsl_data={
        "ai_summary": "...",
        "key_metrics": [...],
        "viewpoints": [...],
        "charts_info": [...]
    },
    report_dsl_status="SUCCESS",
    execution_report="..."
)
```
**预期结果**: 路由到 `__end__`，生成完整报告

## 🎯 测试验证点

### 基本验证
- ✅ 路由结果正确 (`result.goto`)
- ✅ 状态更新正确 (`result.update`)
- ✅ Writer消息生成
- ✅ 无异常抛出

### 业务逻辑验证
- ✅ 不同输入产生不同路由
- ✅ 状态字段正确更新
- ✅ 错误情况正确处理
- ✅ 用户消息正确发送

### 分支覆盖验证
- ✅ 正常流程分支
- ✅ 错误处理分支
- ✅ 边界条件分支
- ✅ 用户交互分支

## 🚀 运行建议

1. **首次运行**: 先运行关键测试验证基本功能
   ```bash
   python run_workflow_tests.py
   ```

2. **调试特定节点**: 运行单个测试进行详细调试
   ```bash
   python run_workflow_tests.py supervisor_greeting
   ```

3. **完整测试**: 运行所有测试用例
   ```bash
   python test_workflow_nodes.py
   ```

## 📝 注意事项

- 测试使用真实的LLM调用，需要配置API密钥
- 某些测试可能因为网络或外部服务而失败
- 测试结果可能因LLM响应的随机性而有所不同
- 建议在稳定的网络环境下运行测试

这些测试用例全面覆盖了workflow节点的主要分支逻辑，可以有效验证各个节点的功能正确性！
