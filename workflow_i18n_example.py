"""
展示如何在workflow.py中使用i18n消息系统的示例
"""

from src.brand_event_agent.messages import (
    agent_msg, status_msg, feedback_msg, msg, set_language
)

# ==================== 使用示例 ====================

def example_supervisor_node_with_i18n(state, writer, config):
    """
    使用i18n的supervisor节点示例
    """
    # 原来的硬编码方式：
    # writer({"live_status_message": "正在分析当前状态和用户消息..."})
    # writer({"agent_message": "请输入具体任务需求，我将为您提供帮助。"})
    
    # 使用i18n的方式：
    writer(status_msg("supervisor.analyzing_state"))
    writer(agent_msg("supervisor.greeting_help"))
    
    # 错误处理
    try:
        # 一些处理逻辑
        pass
    except Exception as e:
        writer(status_msg("supervisor.routing_error"))


def example_intent_clarification_node_with_i18n(state, writer, config):
    """
    使用i18n的意图澄清节点示例
    """
    # 原来的硬编码方式：
    # writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})
    # writer({"live_status_message": "正在分析用户意图..."})
    
    # 使用i18n的方式：
    writer(agent_msg("intent.analyzing_requirements"))
    writer(status_msg("intent.analyzing"))
    
    # 带参数的消息示例
    if clarification_round > 0:
        writer(status_msg("intent.analyzing_reply"))
    
    # 根据不同情况发送不同消息
    if intent_analysis.intent_type == "agreement":
        writer(agent_msg("intent.confirmed"))
    elif intent_analysis.intent_type == "supplement":
        writer(agent_msg("intent.supplement_understood"))
    
    # 人工反馈消息
    writer(feedback_msg("intent.need_more_info"))


def example_execution_node_with_i18n(state, writer, config):
    """
    使用i18n的执行节点示例
    """
    # 原来的硬编码方式：
    # writer({"agent_message": "开始执行分析任务，请稍候..."})
    # writer({"live_status_message": f"开始执行{plan_step.title}..."})
    # writer({"agent_message": f"接下来我将执行第{step_index + 1}个任务：{plan_step.title}"})
    
    # 使用i18n的方式：
    writer(agent_msg("execution.starting"))
    
    # 带参数的消息
    for step_index, plan_step in enumerate(execution_plan.steps):
        step_name = plan_step.title
        writer(status_msg("execution.executing_step", step_name=step_name))
        writer(agent_msg("execution.step_start", 
                        step_index=step_index + 1, 
                        step_title=plan_step.title))
        
        # 执行完成后
        result = "执行结果"
        writer(agent_msg("execution.step_completed", 
                        step_index=step_index + 1, 
                        result=result))
    
    # 所有步骤完成
    writer(agent_msg("execution.all_completed"))
    writer(agent_msg("execution.background_processing"))


def example_report_node_with_i18n(state, writer, config):
    """
    使用i18n的报告节点示例
    """
    # 原来的硬编码方式：
    # writer({"agent_message": "正在生成品牌舆情分析报告..."})
    # writer({"live_status_message": "正在调用外部报告服务..."})
    
    # 使用i18n的方式：
    writer(agent_msg("report.generating"))
    writer(status_msg("report.calling_service"))
    writer(status_msg("report.uploading"))
    writer(status_msg("report.parsing_summary"))
    
    # 错误处理
    if report_dsl_status == "FAILED":
        error_message = report_dsl_message or "未知错误"
        writer(agent_msg("report.generation_failed", error=error_message))
        return
    
    # 成功完成
    writer(agent_msg("report.completed"))
    
    # 带参数的消息
    ai_summary_preview = "这是AI总结预览..."
    writer(agent_msg("report.ai_summary_preview", preview=ai_summary_preview))
    
    # 根据上传结果发送不同消息
    if upload_result["success"]:
        writer(agent_msg("report.file_ready"))
    else:
        upload_message = upload_result.get("message", "")
        writer(agent_msg("report.upload_failed", message=upload_message))
        writer(agent_msg("report.manual_download"))
    
    writer(agent_msg("report.complete_report"))
    writer(feedback_msg("report.other_help"))


def example_error_handling_with_i18n(state, writer, config):
    """
    使用i18n的错误处理示例
    """
    try:
        # 一些可能出错的操作
        pass
    except Exception as e:
        # 原来的硬编码方式：
        # writer({"agent_message": f"执行失败：{str(e)}"})
        
        # 使用i18n的方式：
        writer(agent_msg("errors.step_failed", error=str(e)))


def example_language_switching():
    """
    语言切换示例
    """
    # 默认中文
    print("中文消息:", msg("supervisor.analyzing_state"))
    
    # 切换到英文
    set_language("en_US")
    print("英文消息:", msg("supervisor.analyzing_state"))
    
    # 切换回中文
    set_language("zh_CN")
    print("中文消息:", msg("supervisor.analyzing_state"))


def example_parameter_formatting():
    """
    参数格式化示例
    """
    # 简单参数
    step_name = "数据收集"
    message = msg("execution.executing_step", step_name=step_name)
    print("格式化消息:", message)
    
    # 多个参数
    step_index = 1
    step_title = "品牌数据分析"
    result = "分析完成，发现3个关键洞察"
    
    message = msg("execution.step_completed", 
                  step_index=step_index, 
                  result=result)
    print("多参数消息:", message)


# ==================== 实际应用建议 ====================

"""
在实际的workflow.py中应用i18n的步骤：

1. 导入消息函数：
   from .messages import agent_msg, status_msg, feedback_msg, msg

2. 替换硬编码消息：
   # 原来：
   writer({"agent_message": "正在生成品牌舆情分析报告..."})
   
   # 改为：
   writer(agent_msg("report.generating"))

3. 处理带参数的消息：
   # 原来：
   writer({"agent_message": f"接下来我将执行第{step_index + 1}个任务：{step_title}"})
   
   # 改为：
   writer(agent_msg("execution.step_start", step_index=step_index + 1, step_title=step_title))

4. 错误处理：
   # 原来：
   writer({"agent_message": f"报告生成失败：{error_message}"})
   
   # 改为：
   writer(agent_msg("report.generation_failed", error=error_message))

5. 环境变量配置语言：
   export WORKFLOW_LANGUAGE=en_US  # 设置为英文
   export WORKFLOW_LANGUAGE=zh_CN  # 设置为中文（默认）

优势：
- 支持多语言
- 消息集中管理
- 参数化消息
- 易于维护和修改
- 类型安全（通过IDE提示）
- 支持fallback机制
"""

if __name__ == "__main__":
    # 运行示例
    example_language_switching()
    example_parameter_formatting()
